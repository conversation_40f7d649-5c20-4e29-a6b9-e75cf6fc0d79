# Sales Dialer Application - Testing and Deployment Procedures

## 1. Testing Strategy Overview

### 1.1 Testing Philosophy

**Test-Driven Development (TDD):**
- Write tests before implementation
- Red-Green-Refactor cycle
- High test coverage for critical components
- Automated testing in CI/CD pipeline

**Testing Pyramid:**
- **Unit Tests (70%):** Fast, isolated, comprehensive
- **Integration Tests (20%):** Service interactions, database operations
- **End-to-End Tests (10%):** Complete user workflows

### 1.2 Quality Gates

**Code Quality Standards:**
- Test coverage > 90% for critical components
- No critical security vulnerabilities
- Performance benchmarks met
- Code review approval required
- Documentation completeness

## 2. Unit Testing Framework

### 2.1 Backend Unit Tests (Node.js/Jest)

**Test Configuration:**
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/**/*.test.{js,ts}',
    '!src/migrations/**',
    '!src/seeds/**'
  ],
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: ['**/__tests__/**/*.(test|spec).{js,ts}']
};
```

**Example Unit Tests:**
```javascript
// tests/services/auth.test.js
const AuthService = require('../../src/services/AuthService');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

describe('AuthService', () => {
  let authService;
  let mockUserRepository;

  beforeEach(() => {
    mockUserRepository = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      updateLastLogin: jest.fn()
    };
    authService = new AuthService(mockUserRepository);
  });

  describe('login', () => {
    it('should return JWT token for valid credentials', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';
      const hashedPassword = await bcrypt.hash(password, 10);
      const user = {
        id: 'user-123',
        email,
        password_hash: hashedPassword,
        role: 'agent'
      };

      mockUserRepository.findByEmail.mockResolvedValue(user);
      mockUserRepository.updateLastLogin.mockResolvedValue();

      // Act
      const result = await authService.login(email, password);

      // Assert
      expect(result.success).toBe(true);
      expect(result.token).toBeDefined();
      expect(jwt.decode(result.token).sub).toBe(user.id);
      expect(mockUserRepository.updateLastLogin).toHaveBeenCalledWith(user.id);
    });

    it('should throw error for invalid credentials', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act & Assert
      await expect(authService.login('<EMAIL>', 'wrong'))
        .rejects.toThrow('Invalid credentials');
    });
  });

  describe('validateToken', () => {
    it('should return user data for valid token', async () => {
      // Arrange
      const payload = { sub: 'user-123', role: 'agent' };
      const token = jwt.sign(payload, process.env.JWT_SECRET);

      // Act
      const result = await authService.validateToken(token);

      // Assert
      expect(result.userId).toBe(payload.sub);
      expect(result.role).toBe(payload.role);
    });
  });
});
```

### 2.2 Frontend Unit Tests (React/Jest)

**Component Testing:**
```javascript
// tests/components/ContactList.test.jsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ContactList from '../../src/components/ContactList';
import * as contactService from '../../src/services/contactService';

jest.mock('../../src/services/contactService');

const mockContacts = [
  {
    id: '1',
    first_name: 'John',
    last_name: 'Doe',
    company: 'Acme Corp',
    phone: '+**********'
  },
  {
    id: '2',
    first_name: 'Jane',
    last_name: 'Smith',
    company: 'Tech Solutions',
    phone: '+**********'
  }
];

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('ContactList', () => {
  beforeEach(() => {
    contactService.getContacts.mockResolvedValue({
      contacts: mockContacts,
      pagination: { total: 2, page: 1, totalPages: 1 }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render contact list correctly', async () => {
    // Act
    renderWithRouter(<ContactList />);

    // Assert
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Acme Corp')).toBeInTheDocument();
    });
  });

  it('should filter contacts when search is performed', async () => {
    // Arrange
    contactService.getContacts.mockResolvedValueOnce({
      contacts: [mockContacts[0]],
      pagination: { total: 1, page: 1, totalPages: 1 }
    });

    renderWithRouter(<ContactList />);

    // Act
    const searchInput = screen.getByPlaceholderText('Search contacts...');
    fireEvent.change(searchInput, { target: { value: 'John' } });
    fireEvent.click(screen.getByText('Search'));

    // Assert
    await waitFor(() => {
      expect(contactService.getContacts).toHaveBeenCalledWith({
        search: 'John',
        page: 1,
        limit: 50
      });
    });
  });
});
```

## 3. Integration Testing

### 3.1 API Integration Tests

**Database Integration Testing:**
```javascript
// tests/integration/contact.integration.test.js
const request = require('supertest');
const app = require('../../src/app');
const { setupTestDatabase, cleanupTestDatabase } = require('../helpers/database');

describe('Contact API Integration', () => {
  let authToken;
  let testUser;

  beforeAll(async () => {
    await setupTestDatabase();
    
    // Create test user and get auth token
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Test',
        last_name: 'User'
      });

    testUser = userResponse.body.data.user;

    const loginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });

    authToken = loginResponse.body.data.access_token;
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  describe('POST /api/v1/contacts', () => {
    it('should create a new contact', async () => {
      // Arrange
      const contactData = {
        first_name: 'John',
        last_name: 'Doe',
        company: 'Acme Corp',
        email: '<EMAIL>',
        phone: '+**********'
      };

      // Act
      const response = await request(app)
        .post('/api/v1/contacts')
        .set('Authorization', `Bearer ${authToken}`)
        .send(contactData);

      // Assert
      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.first_name).toBe(contactData.first_name);
      expect(response.body.data.id).toBeDefined();
    });

    it('should return validation error for invalid data', async () => {
      // Act
      const response = await request(app)
        .post('/api/v1/contacts')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          first_name: '', // Invalid: empty name
          email: 'invalid-email' // Invalid: malformed email
        });

      // Assert
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/v1/contacts', () => {
    it('should return paginated contact list', async () => {
      // Act
      const response = await request(app)
        .get('/api/v1/contacts?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.contacts).toBeInstanceOf(Array);
      expect(response.body.data.pagination).toBeDefined();
    });
  });
});
```

### 3.2 External Service Integration Tests

**Twilio Integration Testing:**
```javascript
// tests/integration/telephony.integration.test.js
const TelephonyService = require('../../src/services/TelephonyService');
const nock = require('nock');

describe('TelephonyService Integration', () => {
  let telephonyService;

  beforeEach(() => {
    telephonyService = new TelephonyService({
      accountSid: 'test_account_sid',
      authToken: 'test_auth_token'
    });
  });

  afterEach(() => {
    nock.cleanAll();
  });

  describe('initiateCall', () => {
    it('should successfully initiate a call via Twilio', async () => {
      // Arrange
      const mockCallResponse = {
        sid: 'CA*********',
        status: 'queued',
        from: '+**********',
        to: '+**********'
      };

      nock('https://api.twilio.com')
        .post('/2010-04-01/Accounts/test_account_sid/Calls.json')
        .reply(201, mockCallResponse);

      // Act
      const result = await telephonyService.initiateCall({
        from: '+**********',
        to: '+**********',
        url: 'http://example.com/twiml'
      });

      // Assert
      expect(result.sid).toBe(mockCallResponse.sid);
      expect(result.status).toBe('queued');
    });

    it('should handle Twilio API errors gracefully', async () => {
      // Arrange
      nock('https://api.twilio.com')
        .post('/2010-04-01/Accounts/test_account_sid/Calls.json')
        .reply(400, {
          code: 21211,
          message: 'Invalid phone number format'
        });

      // Act & Assert
      await expect(telephonyService.initiateCall({
        from: 'invalid',
        to: '+**********',
        url: 'http://example.com/twiml'
      })).rejects.toThrow('Invalid phone number format');
    });
  });
});
```

## 4. End-to-End Testing

### 4.1 E2E Test Framework (Playwright)

**Configuration:**
```javascript
// playwright.config.js
module.exports = {
  testDir: './tests/e2e',
  timeout: 30000,
  retries: 2,
  use: {
    baseURL: 'http://localhost:3000',
    headless: true,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ]
};
```

**E2E Test Examples:**
```javascript
// tests/e2e/call-workflow.spec.js
const { test, expect } = require('@playwright/test');

test.describe('Call Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password123');
    await page.click('[data-testid=login-button]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should complete full call workflow', async ({ page }) => {
    // Navigate to contacts
    await page.click('[data-testid=contacts-nav]');
    await expect(page).toHaveURL('/contacts');

    // Select a contact
    await page.click('[data-testid=contact-item]:first-child');
    await expect(page.locator('[data-testid=contact-details]')).toBeVisible();

    // Initiate call
    await page.click('[data-testid=call-button]');
    await expect(page.locator('[data-testid=call-interface]')).toBeVisible();

    // Verify call status
    await expect(page.locator('[data-testid=call-status]')).toHaveText('Connecting...');

    // Wait for call to connect (mock)
    await page.waitForSelector('[data-testid=call-connected]', { timeout: 10000 });
    await expect(page.locator('[data-testid=call-status]')).toHaveText('Connected');

    // Verify transcription appears
    await expect(page.locator('[data-testid=transcription-area]')).toBeVisible();

    // End call
    await page.click('[data-testid=hangup-button]');
    await expect(page.locator('[data-testid=call-summary]')).toBeVisible();

    // Verify call is saved
    await page.click('[data-testid=save-call-button]');
    await expect(page.locator('[data-testid=success-message]')).toBeVisible();
  });

  test('should handle call failures gracefully', async ({ page }) => {
    // Mock network failure
    await page.route('**/api/v1/calls/initiate', route => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Service unavailable' })
      });
    });

    // Attempt to make call
    await page.goto('/contacts');
    await page.click('[data-testid=contact-item]:first-child');
    await page.click('[data-testid=call-button]');

    // Verify error handling
    await expect(page.locator('[data-testid=error-message]')).toBeVisible();
    await expect(page.locator('[data-testid=error-message]')).toContainText('Unable to initiate call');
  });
});
```

## 5. Performance Testing

### 5.1 Load Testing with k6

**Load Test Scripts:**
```javascript
// tests/performance/api-load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

export let errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up
    { duration: '5m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 100 }, // Ramp up to 100 users
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 0 }, // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'], // Error rate under 10%
    errors: ['rate<0.1'],
  },
};

const BASE_URL = 'http://localhost:3000/api/v1';

export function setup() {
  // Login and get auth token
  const loginResponse = http.post(`${BASE_URL}/auth/login`, {
    email: '<EMAIL>',
    password: 'password123'
  });

  return {
    authToken: loginResponse.json('data.access_token')
  };
}

export default function(data) {
  const headers = {
    'Authorization': `Bearer ${data.authToken}`,
    'Content-Type': 'application/json'
  };

  // Test contact list endpoint
  let response = http.get(`${BASE_URL}/contacts?page=1&limit=50`, { headers });
  
  let success = check(response, {
    'contacts list status is 200': (r) => r.status === 200,
    'contacts list response time < 500ms': (r) => r.timings.duration < 500,
    'contacts list has data': (r) => r.json('data.contacts').length > 0,
  });

  errorRate.add(!success);

  sleep(1);

  // Test call initiation endpoint
  response = http.post(`${BASE_URL}/calls/initiate`, JSON.stringify({
    phone_number: '+**********',
    recording_enabled: true
  }), { headers });

  success = check(response, {
    'call initiate status is 200': (r) => r.status === 200,
    'call initiate response time < 1000ms': (r) => r.timings.duration < 1000,
  });

  errorRate.add(!success);

  sleep(2);
}
```

### 5.2 Database Performance Testing

**Database Load Testing:**
```javascript
// tests/performance/database-performance.test.js
const { Pool } = require('pg');
const { performance } = require('perf_hooks');

describe('Database Performance', () => {
  let pool;

  beforeAll(async () => {
    pool = new Pool({
      connectionString: process.env.TEST_DATABASE_URL,
      max: 20
    });
  });

  afterAll(async () => {
    await pool.end();
  });

  test('contact search should perform under 100ms', async () => {
    const startTime = performance.now();

    const result = await pool.query(`
      SELECT id, first_name, last_name, company, phone
      FROM contacts
      WHERE organization_id = $1
        AND (first_name ILIKE $2 OR last_name ILIKE $2 OR company ILIKE $2)
      ORDER BY last_name, first_name
      LIMIT 50
    `, ['org-123', '%john%']);

    const endTime = performance.now();
    const duration = endTime - startTime;

    expect(duration).toBeLessThan(100);
    expect(result.rows).toBeDefined();
  });

  test('concurrent call insertions should handle 100 operations', async () => {
    const promises = [];
    const startTime = performance.now();

    for (let i = 0; i < 100; i++) {
      promises.push(
        pool.query(`
          INSERT INTO calls (organization_id, user_id, phone_number, status)
          VALUES ($1, $2, $3, $4)
          RETURNING id
        `, ['org-123', 'user-123', `+*********${i}`, 'completed'])
      );
    }

    await Promise.all(promises);
    const endTime = performance.now();
    const duration = endTime - startTime;

    expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
  });
});
```

## 6. Deployment Procedures

### 6.1 CI/CD Pipeline Configuration

**GitHub Actions Workflow:**
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run unit tests
        run: npm run test:unit
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379

      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3

  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run security audit
        run: npm audit --audit-level high

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  build:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker images
        run: |
          docker build -t salesdialer/frontend:${{ github.sha }} ./frontend
          docker build -t salesdialer/api-gateway:${{ github.sha }} ./backend/api-gateway
          docker build -t salesdialer/transcription:${{ github.sha }} ./backend/transcription-service
          
          docker push salesdialer/frontend:${{ github.sha }}
          docker push salesdialer/api-gateway:${{ github.sha }}
          docker push salesdialer/transcription:${{ github.sha }}

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment"
          # Add deployment commands here

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Deploy to production
        run: |
          echo "Deploying to production environment"
          # Add production deployment commands here
```

### 6.2 Production Deployment Script

**Deployment Automation:**
```bash
#!/bin/bash
# deploy-production.sh

set -e

ENVIRONMENT="production"
DOCKER_COMPOSE_FILE="docker-compose.${ENVIRONMENT}.yml"
BACKUP_DIR="/backups/$(date +%Y%m%d_%H%M%S)"

echo "Starting production deployment for Sales Dialer Application"

# Pre-deployment checks
echo "Running pre-deployment checks..."

# Check if all required environment variables are set
required_vars=("DATABASE_URL" "JWT_SECRET" "TWILIO_ACCOUNT_SID" "TWILIO_AUTH_TOKEN")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "Error: Required environment variable $var is not set"
        exit 1
    fi
done

# Check disk space
available_space=$(df / | awk 'NR==2 {print $4}')
required_space=5000000  # 5GB in KB
if [[ $available_space -lt $required_space ]]; then
    echo "Error: Insufficient disk space. Required: 5GB, Available: $(($available_space/1000000))GB"
    exit 1
fi

# Create backup
echo "Creating database backup..."
mkdir -p "$BACKUP_DIR"
docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump -U postgres salesdialer > "$BACKUP_DIR/database_backup.sql"

# Pull latest images
echo "Pulling latest Docker images..."
docker-compose -f "$DOCKER_COMPOSE_FILE" pull

# Run database migrations
echo "Running database migrations..."
docker-compose -f "$DOCKER_COMPOSE_FILE" run --rm api-gateway npm run migrate

# Deploy with zero downtime
echo "Deploying application..."
docker-compose -f "$DOCKER_COMPOSE_FILE" up -d --remove-orphans

# Health checks
echo "Performing health checks..."
sleep 30

services=("frontend" "api-gateway" "telephony-service" "transcription-service")
for service in "${services[@]}"; do
    if ! docker-compose -f "$DOCKER_COMPOSE_FILE" ps "$service" | grep -q "Up"; then
        echo "Error: Service $service is not running"
        echo "Rolling back deployment..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        # Restore from backup if needed
        exit 1
    fi
done

# Verify API endpoints
echo "Verifying API endpoints..."
if ! curl -f http://localhost/api/v1/health > /dev/null 2>&1; then
    echo "Error: API health check failed"
    exit 1
fi

# Clean up old images
echo "Cleaning up old Docker images..."
docker image prune -f

echo "Production deployment completed successfully!"
echo "Backup created at: $BACKUP_DIR"
```

This comprehensive testing and deployment strategy ensures the Sales Dialer Application is thoroughly tested at all levels and can be deployed reliably to production with proper safeguards and rollback procedures.
