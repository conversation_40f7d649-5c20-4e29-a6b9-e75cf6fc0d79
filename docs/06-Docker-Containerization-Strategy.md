# Sales Dialer Application - Docker Containerization Strategy

## 1. Containerization Overview

### 1.1 Container Architecture Strategy

**Microservices Containerization:**
- One service per container for optimal scaling
- Shared base images for consistency
- Multi-stage builds for optimized image sizes
- Security-hardened containers with minimal attack surface

**Container Orchestration:**
- Docker Compose for local development
- Docker Swarm for production deployment
- Kubernetes support for enterprise scaling
- Health checks and auto-restart policies

### 1.2 Image Optimization Strategy

**Base Image Selection:**
- Alpine Linux for minimal footprint
- Node.js Alpine for JavaScript services
- Python Alpine for AI/ML services
- Distroless images for production security

**Build Optimization:**
- Multi-stage builds to reduce image size
- Layer caching for faster builds
- .dockerignore for excluding unnecessary files
- Dependency optimization and cleanup

## 2. Service Container Definitions

### 2.1 Frontend Container

**Dockerfile for React Frontend:**
```dockerfile
# Multi-stage build for React frontend
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/
COPY public/ ./public/
COPY tsconfig.json ./

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=builder /app/build /usr/share/nginx/html

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

# Security: Run as non-root user
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

USER nginx

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

**Nginx Configuration:**
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # React app routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API proxy
        location /api/ {
            proxy_pass http://api-gateway:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # WebSocket proxy
        location /ws/ {
            proxy_pass http://api-gateway:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
```

### 2.2 API Gateway Container

**Dockerfile for API Gateway:**
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# Copy package files
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/
COPY config/ ./config/

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -D -H -u 1001 -h /app -s /sbin/nologin -G nodejs -g nodejs nodejs

# Change ownership
RUN chown -R nodejs:nodejs /app

USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js || exit 1

EXPOSE 3000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "src/server.js"]
```

### 2.3 Transcription Service Container

**Dockerfile for AI/ML Service:**
```dockerfile
# Multi-stage build for Python AI service
FROM python:3.11-slim AS base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Production stage
FROM base AS production

# Copy application code
COPY src/ ./src/
COPY models/ ./models/

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app

USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD python src/healthcheck.py || exit 1

EXPOSE 8000

CMD ["python", "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2.4 Database Containers

**PostgreSQL with Custom Configuration:**
```dockerfile
FROM postgres:15-alpine

# Copy custom configuration
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf

# Copy initialization scripts
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Install extensions
RUN apk add --no-cache postgresql-contrib

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
  CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1

EXPOSE 5432
```

## 3. Docker Compose Configuration

### 3.1 Monolithic Deployment (Original Specification Compliance)

**docker-compose.monolith.yml:**
```yaml
version: '3.8'

services:
  # Single application container (matches original specification)
  app:
    build:
      context: .
      dockerfile: Dockerfile.monolith
    ports:
      - "3000:3000"
    volumes:
      - ./calls:/app/calls
    environment:
      - NODE_ENV=development
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - GOOGLE_DRIVE_FOLDER_ID=${GOOGLE_DRIVE_FOLDER_ID}
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - CRM_API_URL=${CRM_API_URL}
      - DATABASE_URL=********************************************/salesdialer
    depends_on:
      - postgres
      - minio
      - n8n
    restart: unless-stopped

  # Supporting services (as per original spec)
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=salesdialer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  minio:
    image: minio/minio
    ports:
      - "9000:9000"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
    volumes:
      - minio_data:/data
    command: server /data
    restart: unless-stopped

  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped

volumes:
  postgres_data:
  minio_data:
  n8n_data:
```

**Dockerfile.monolith (Original Specification):**
```dockerfile
# Base image (updated from node:14 to node:18 for security)
FROM node:18

# Install dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    ffmpeg \
    rclone \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Python dependencies for transcription
RUN pip3 install whisper deepspeech

# Install Node.js dependencies
COPY package*.json ./
RUN npm install

# Copy application files
COPY . .

# Expose the application port
EXPOSE 3000

# Command to run the application
CMD ["npm", "start"]
```

### 3.2 Development Environment (Microservices)

**docker-compose.dev.yml:**
```yaml
version: '3.8'

services:
  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    environment:
      - REACT_APP_API_URL=http://localhost:3001
      - REACT_APP_WS_URL=ws://localhost:3001
    depends_on:
      - api-gateway

  # API Gateway
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3000"
    volumes:
      - ./backend/api-gateway/src:/app/src
    environment:
      - NODE_ENV=development
      - JWT_SECRET=dev-secret-key
      - DATABASE_URL=********************************************/salesdialer
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Telephony Service
  telephony-service:
    build:
      context: ./backend/telephony-service
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - DATABASE_URL=********************************************/salesdialer
    depends_on:
      - postgres
    restart: unless-stopped

  # Transcription Service
  transcription-service:
    build:
      context: ./backend/transcription-service
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - MODEL_PATH=/app/models/whisper-base
      - GPU_ENABLED=false
    volumes:
      - ./models:/app/models
      - ./temp:/app/temp
    restart: unless-stopped

  # Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=salesdialer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    restart: unless-stopped

  # Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Storage
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped

  # Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data:
  n8n_data:

networks:
  default:
    driver: bridge
```

### 3.2 Production Environment

**docker-compose.prod.yml:**
```yaml
version: '3.8'

services:
  # Load Balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - api-gateway
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    expose:
      - "80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # API Gateway
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=${JWT_SECRET}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    secrets:
      - jwt_private_key
      - jwt_public_key
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Transcription Service with GPU
  transcription-service:
    build:
      context: ./backend/transcription-service
      dockerfile: Dockerfile
    expose:
      - "8000"
    environment:
      - ENVIRONMENT=production
      - MODEL_PATH=/app/models/whisper-large
      - GPU_ENABLED=true
    volumes:
      - ./models:/app/models:ro
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Database with replication
  postgres-primary:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_REPLICATION_MODE=master
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=${POSTGRES_REPLICATION_PASSWORD}
    volumes:
      - postgres_primary_data:/var/lib/postgresql/data
    restart: unless-stopped

  postgres-replica:
    image: postgres:15-alpine
    environment:
      - POSTGRES_MASTER_SERVICE=postgres-primary
      - POSTGRES_REPLICATION_MODE=slave
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=${POSTGRES_REPLICATION_PASSWORD}
    volumes:
      - postgres_replica_data:/var/lib/postgresql/data
    restart: unless-stopped

secrets:
  jwt_private_key:
    external: true
  jwt_public_key:
    external: true

volumes:
  postgres_primary_data:
  postgres_replica_data:

networks:
  frontend:
    driver: overlay
  backend:
    driver: overlay
    internal: true
```

## 4. Container Security Hardening

### 4.1 Security Best Practices

**Dockerfile Security:**
```dockerfile
# Security-hardened base image
FROM node:18-alpine

# Install security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S -D -H -u 1001 -h /app -s /sbin/nologin -G appgroup -g appgroup appuser

WORKDIR /app

# Copy and install dependencies as root
COPY package*.json ./
RUN npm ci --only=production && \
    npm cache clean --force && \
    chown -R appuser:appgroup /app

# Copy application code
COPY --chown=appuser:appgroup . .

# Switch to non-root user
USER appuser

# Remove unnecessary packages and files
RUN rm -rf /tmp/* /var/tmp/* /root/.npm

# Set security options
LABEL security.scan="enabled"
LABEL security.non-root="true"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js || exit 1

EXPOSE 3000

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]
```

### 4.2 Container Runtime Security

**Docker Compose Security Configuration:**
```yaml
services:
  api-gateway:
    build: .
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    user: "1001:1001"
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 5. Monitoring and Logging

### 5.1 Container Monitoring

**Prometheus Configuration:**
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources

  node-exporter:
    image: prom/node-exporter:latest
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'

volumes:
  prometheus_data:
  grafana_data:
```

### 5.2 Centralized Logging

**ELK Stack Configuration:**
```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

## 6. Deployment and Scaling

### 6.1 Production Deployment Script

**deploy.sh:**
```bash
#!/bin/bash

set -e

# Configuration
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.${ENVIRONMENT}.yml"

echo "Deploying Sales Dialer Application to ${ENVIRONMENT}"

# Pull latest images
docker-compose -f ${COMPOSE_FILE} pull

# Build custom images
docker-compose -f ${COMPOSE_FILE} build --no-cache

# Run database migrations
docker-compose -f ${COMPOSE_FILE} run --rm api-gateway npm run migrate

# Deploy with zero downtime
docker-compose -f ${COMPOSE_FILE} up -d --remove-orphans

# Health check
echo "Waiting for services to be healthy..."
sleep 30

# Verify deployment
docker-compose -f ${COMPOSE_FILE} ps
docker-compose -f ${COMPOSE_FILE} logs --tail=50

echo "Deployment completed successfully!"
```

### 6.2 Auto-scaling Configuration

**Docker Swarm Auto-scaling:**
```yaml
version: '3.8'

services:
  api-gateway:
    image: salesdialer/api-gateway:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    networks:
      - backend

networks:
  backend:
    driver: overlay
    attachable: true
```

This containerization strategy provides a robust, secure, and scalable foundation for deploying the Sales Dialer Application across different environments while maintaining consistency and security.
