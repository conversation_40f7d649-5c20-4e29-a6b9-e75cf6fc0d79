# Sales Dialer Application - API Design and Endpoint Specifications

## 1. API Architecture Overview

### 1.1 RESTful API Design Principles

**Core Principles:**
- RESTful resource-based URLs
- HTTP methods for CRUD operations
- Consistent response formats
- Stateless authentication with JWT
- Versioning through URL path (/api/v1/)

**Response Format:**
```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

**Error Format:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 1.2 Authentication and Authorization

**JWT Token Structure:**
```json
{
  "sub": "user_id",
  "org": "organization_id",
  "role": "agent",
  "permissions": ["calls:read", "calls:write", "contacts:read"],
  "exp": 1642248000,
  "iat": 1642244400
}
```

**Authorization Headers:**
```
Authorization: Bearer <jwt_token>
X-Organization-ID: <organization_id>
X-Request-ID: <unique_request_id>
```

## 2. Authentication Endpoints

### 2.1 User Authentication

**POST /api/v1/auth/login**
```json
// Request
{
  "email": "<EMAIL>",
  "password": "secure_password",
  "remember_me": false
}

// Response
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 3600,
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "role": "agent",
      "organization": {
        "id": "org_456",
        "name": "Acme Corp"
      }
    }
  }
}
```

**POST /api/v1/auth/refresh**
```json
// Request
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}

// Response
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 3600
  }
}
```

**POST /api/v1/auth/logout**
```json
// Request (Headers only)
Authorization: Bearer <access_token>

// Response
{
  "success": true,
  "message": "Successfully logged out"
}
```

### 2.2 User Management

**GET /api/v1/users/profile**
```json
// Response
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "agent",
    "organization_id": "org_456",
    "last_login_at": "2024-01-15T09:30:00Z",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

**PUT /api/v1/users/profile**
```json
// Request
{
  "first_name": "John",
  "last_name": "Smith",
  "phone": "+**********"
}

// Response
{
  "success": true,
  "data": {
    "id": "user_123",
    "first_name": "John",
    "last_name": "Smith",
    "phone": "+**********",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

## 3. Contact Management Endpoints

### 3.1 Contact CRUD Operations

**GET /api/v1/contacts**
```json
// Query Parameters: ?page=1&limit=50&search=john&group_id=group_123&sort=last_name

// Response
{
  "success": true,
  "data": {
    "contacts": [
      {
        "id": "contact_123",
        "first_name": "John",
        "last_name": "Doe",
        "company": "Acme Corp",
        "title": "Sales Manager",
        "email": "<EMAIL>",
        "phone": "+**********",
        "mobile_phone": "+1234567891",
        "tags": ["prospect", "high-value"],
        "last_contacted_at": "2024-01-10T14:30:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "total_pages": 3
    }
  }
}
```

**POST /api/v1/contacts**
```json
// Request
{
  "first_name": "Jane",
  "last_name": "Smith",
  "company": "Tech Solutions",
  "title": "CTO",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "tags": ["prospect"],
  "custom_fields": {
    "industry": "Technology",
    "company_size": "50-100"
  }
}

// Response
{
  "success": true,
  "data": {
    "id": "contact_456",
    "first_name": "Jane",
    "last_name": "Smith",
    "company": "Tech Solutions",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

**GET /api/v1/contacts/{contact_id}**
```json
// Response
{
  "success": true,
  "data": {
    "id": "contact_123",
    "first_name": "John",
    "last_name": "Doe",
    "company": "Acme Corp",
    "title": "Sales Manager",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zip": "10001",
      "country": "US"
    },
    "custom_fields": {},
    "tags": ["prospect", "high-value"],
    "recent_calls": [
      {
        "id": "call_789",
        "started_at": "2024-01-10T14:30:00Z",
        "duration_seconds": 300,
        "status": "completed"
      }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-10T14:35:00Z"
  }
}
```

### 3.2 Contact Groups

**GET /api/v1/contact-groups**
```json
// Response
{
  "success": true,
  "data": {
    "groups": [
      {
        "id": "group_123",
        "name": "High Value Prospects",
        "description": "Contacts with high potential value",
        "contact_count": 25,
        "is_dynamic": false,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

**POST /api/v1/contact-groups**
```json
// Request
{
  "name": "Q1 Targets",
  "description": "Contacts to focus on in Q1",
  "contact_ids": ["contact_123", "contact_456"]
}

// Response
{
  "success": true,
  "data": {
    "id": "group_789",
    "name": "Q1 Targets",
    "contact_count": 2,
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

## 4. Call Management Endpoints

### 4.1 Call Operations

**POST /api/v1/calls/initiate**
```json
// Request
{
  "contact_id": "contact_123",
  "phone_number": "+**********",
  "recording_enabled": true,
  "transcription_enabled": true
}

// Response
{
  "success": true,
  "data": {
    "call_id": "call_789",
    "status": "initiated",
    "provider_call_id": "twilio_call_123",
    "started_at": "2024-01-15T10:30:00Z"
  }
}
```

**GET /api/v1/calls**
```json
// Query Parameters: ?page=1&limit=50&status=completed&date_from=2024-01-01&date_to=2024-01-15

// Response
{
  "success": true,
  "data": {
    "calls": [
      {
        "id": "call_789",
        "contact": {
          "id": "contact_123",
          "name": "John Doe",
          "company": "Acme Corp"
        },
        "phone_number": "+**********",
        "direction": "outbound",
        "status": "completed",
        "started_at": "2024-01-15T10:30:00Z",
        "ended_at": "2024-01-15T10:35:00Z",
        "duration_seconds": 300,
        "recording_available": true,
        "transcription_available": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 100,
      "total_pages": 2
    }
  }
}
```

**GET /api/v1/calls/{call_id}**
```json
// Response
{
  "success": true,
  "data": {
    "id": "call_789",
    "contact": {
      "id": "contact_123",
      "name": "John Doe",
      "company": "Acme Corp",
      "phone": "+**********"
    },
    "direction": "outbound",
    "status": "completed",
    "started_at": "2024-01-15T10:30:00Z",
    "answered_at": "2024-01-15T10:30:05Z",
    "ended_at": "2024-01-15T10:35:00Z",
    "duration_seconds": 300,
    "recording": {
      "id": "recording_456",
      "format": "opus",
      "duration_seconds": 300,
      "file_size_bytes": 1024000,
      "download_url": "/api/v1/recordings/recording_456/download"
    },
    "transcription": {
      "id": "transcription_789",
      "content": "[00:00:00] Agent: Hello, this is John from...",
      "confidence_score": 0.95,
      "language": "en"
    },
    "analytics": {
      "talk_time_seconds": 180,
      "listen_time_seconds": 120,
      "sentiment_score": 0.7,
      "summary": "Discussed product features and pricing"
    }
  }
}
```

### 4.2 Call Control

**POST /api/v1/calls/{call_id}/answer**
```json
// Response
{
  "success": true,
  "data": {
    "call_id": "call_789",
    "status": "answered",
    "answered_at": "2024-01-15T10:30:05Z"
  }
}
```

**POST /api/v1/calls/{call_id}/hangup**
```json
// Response
{
  "success": true,
  "data": {
    "call_id": "call_789",
    "status": "completed",
    "ended_at": "2024-01-15T10:35:00Z",
    "duration_seconds": 300
  }
}
```

**POST /api/v1/calls/{call_id}/hold**
```json
// Request
{
  "hold": true
}

// Response
{
  "success": true,
  "data": {
    "call_id": "call_789",
    "on_hold": true
  }
}
```

**POST /api/v1/calls/{call_id}/dtmf**
```json
// Request - Send DTMF tones (telephone keypad)
{
  "digits": "1234#"
}

// Response
{
  "success": true,
  "data": {
    "call_id": "call_789",
    "digits_sent": "1234#",
    "timestamp": "2024-01-15T10:30:20Z"
  }
}
```

## 5. Recording and Transcription Endpoints

### 5.1 Recording Management

**GET /api/v1/recordings/{recording_id}/download**
```
// Response: Binary audio file with appropriate headers
Content-Type: audio/opus
Content-Disposition: attachment; filename="call_789_recording.opus"
Content-Length: 1024000
```

**GET /api/v1/recordings/{recording_id}/stream**
```
// Response: Streaming audio with range support
Content-Type: audio/opus
Accept-Ranges: bytes
Content-Range: bytes 0-1023999/1024000
```

### 5.2 Transcription Management

**GET /api/v1/transcriptions/{transcription_id}**
```json
// Response
{
  "success": true,
  "data": {
    "id": "transcription_789",
    "call_id": "call_789",
    "content": "[00:00:00] Agent: Hello, this is John from Acme Corp...",
    "language": "en",
    "confidence_score": 0.95,
    "speaker_segments": [
      {
        "speaker": "Agent",
        "start_time": 0,
        "end_time": 5.2,
        "text": "Hello, this is John from Acme Corp"
      },
      {
        "speaker": "Customer",
        "start_time": 5.5,
        "end_time": 8.1,
        "text": "Hi John, how can I help you?"
      }
    ],
    "keywords": ["product", "pricing", "demo"],
    "sentiment_analysis": {
      "overall_sentiment": "positive",
      "sentiment_score": 0.7,
      "emotions": ["interested", "curious"]
    },
    "created_at": "2024-01-15T10:35:30Z"
  }
}
```

**POST /api/v1/transcriptions/{transcription_id}/regenerate**
```json
// Request
{
  "model": "whisper-large",
  "language": "en",
  "speaker_diarization": true
}

// Response
{
  "success": true,
  "data": {
    "job_id": "job_123",
    "status": "queued",
    "estimated_completion": "2024-01-15T10:40:00Z"
  }
}
```

## 6. Real-time WebSocket API

### 6.1 WebSocket Connection

**Connection URL:** `wss://api.example.com/ws/v1/calls/{call_id}`

**Authentication:** JWT token in query parameter or header

### 6.2 Real-time Events

**Call Status Updates:**
```json
{
  "type": "call_status",
  "data": {
    "call_id": "call_789",
    "status": "answered",
    "timestamp": "2024-01-15T10:30:05Z"
  }
}
```

**Real-time Transcription:**
```json
{
  "type": "transcription_chunk",
  "data": {
    "call_id": "call_789",
    "speaker": "Agent",
    "text": "Hello, this is John",
    "confidence": 0.95,
    "is_final": false,
    "timestamp": "2024-01-15T10:30:10Z"
  }
}
```

**Audio Quality Metrics:**
```json
{
  "type": "audio_quality",
  "data": {
    "call_id": "call_789",
    "quality_score": 0.85,
    "latency_ms": 150,
    "packet_loss": 0.01,
    "timestamp": "2024-01-15T10:30:15Z"
  }
}
```

## 7. Analytics and Reporting Endpoints

### 7.1 Call Analytics

**GET /api/v1/analytics/calls/summary**
```json
// Query Parameters: ?date_from=2024-01-01&date_to=2024-01-15&user_id=user_123

// Response
{
  "success": true,
  "data": {
    "total_calls": 150,
    "completed_calls": 120,
    "average_duration": 285,
    "total_talk_time": 34200,
    "success_rate": 0.8,
    "by_day": [
      {
        "date": "2024-01-15",
        "calls": 12,
        "duration": 3420
      }
    ]
  }
}
```

**GET /api/v1/analytics/transcriptions/keywords**
```json
// Response
{
  "success": true,
  "data": {
    "keywords": [
      {
        "keyword": "pricing",
        "frequency": 45,
        "sentiment": 0.6
      },
      {
        "keyword": "demo",
        "frequency": 32,
        "sentiment": 0.8
      }
    ]
  }
}
```

This API design provides a comprehensive foundation for the Sales Dialer Application with proper authentication, real-time capabilities, and extensive functionality for managing calls, contacts, and analytics.
