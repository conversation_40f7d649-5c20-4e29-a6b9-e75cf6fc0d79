# Sales Dialer Application - Database Schema and Data Management Strategy

## 1. Database Architecture Overview

### 1.1 Multi-Database Strategy

**Primary Database: PostgreSQL**
- Transactional data (users, contacts, calls)
- ACID compliance for critical operations
- JSON support for flexible metadata
- Full-text search capabilities

**Cache Layer: Redis**
- Session management and authentication tokens
- Real-time data caching
- Message queuing for async operations
- Rate limiting and throttling

**Time-Series Database: InfluxDB (Optional)**
- Call analytics and metrics
- Performance monitoring data
- Real-time dashboard feeds
- Historical trend analysis

### 1.2 Data Distribution Strategy

**Hot Data (PostgreSQL)**
- Active users and sessions
- Recent contacts and call history (last 30 days)
- Current transcriptions and metadata
- System configuration and settings

**Warm Data (PostgreSQL + Archive)**
- Historical call records (30-365 days)
- Archived transcriptions and metadata
- User activity logs
- Compliance and audit data

**Cold Data (Object Storage)**
- Audio recordings older than 90 days
- Archived transcriptions (compressed)
- Backup and disaster recovery data
- Long-term compliance storage

## 2. Core Database Schema

### 2.1 User Management and Authentication

```sql
-- Users table with role-based access
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'agent',
    organization_id UUID REFERENCES organizations(id),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_role CHECK (role IN ('admin', 'manager', 'agent', 'viewer'))
);

-- Organizations for multi-tenancy
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    settings JSONB DEFAULT '{}',
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User sessions and tokens
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API keys for external integrations
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    permissions JSONB DEFAULT '[]',
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.2 Contact Management

```sql
-- Contacts with CRM integration
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    external_id VARCHAR(255), -- CRM system ID
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    company VARCHAR(255),
    title VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    mobile_phone VARCHAR(50),
    address JSONB,
    custom_fields JSONB DEFAULT '{}',
    tags TEXT[],
    status VARCHAR(50) DEFAULT 'active',
    last_contacted_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_status CHECK (status IN ('active', 'inactive', 'do_not_call'))
);

-- Contact groups for organization
CREATE TABLE contact_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    criteria JSONB, -- Dynamic group criteria
    is_dynamic BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contact group memberships
CREATE TABLE contact_group_members (
    contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    group_id UUID REFERENCES contact_groups(id) ON DELETE CASCADE,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    added_by UUID REFERENCES users(id),
    
    PRIMARY KEY (contact_id, group_id)
);

-- Contact interaction history
CREATE TABLE contact_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    interaction_type VARCHAR(50) NOT NULL,
    subject VARCHAR(255),
    notes TEXT,
    outcome VARCHAR(100),
    scheduled_follow_up TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_interaction_type CHECK (
        interaction_type IN ('call', 'email', 'meeting', 'note', 'task')
    )
);
```

### 2.3 Call Management and Recording

```sql
-- Call sessions and metadata
CREATE TABLE calls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    user_id UUID NOT NULL REFERENCES users(id),
    contact_id UUID REFERENCES contacts(id),
    phone_number VARCHAR(50) NOT NULL,
    direction VARCHAR(20) NOT NULL DEFAULT 'outbound',
    status VARCHAR(50) NOT NULL DEFAULT 'initiated',
    provider VARCHAR(50) NOT NULL,
    provider_call_id VARCHAR(255),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    answered_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    recording_enabled BOOLEAN DEFAULT true,
    transcription_enabled BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_direction CHECK (direction IN ('inbound', 'outbound')),
    CONSTRAINT valid_status CHECK (
        status IN ('initiated', 'ringing', 'answered', 'completed', 'failed', 'cancelled')
    )
);

-- Call recordings with multiple storage backends
CREATE TABLE call_recordings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_id UUID NOT NULL REFERENCES calls(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL DEFAULT 'recording.opus',
    format VARCHAR(20) NOT NULL DEFAULT 'opus',
    duration_seconds INTEGER,
    file_size_bytes BIGINT,
    checksum VARCHAR(64),
    storage_path VARCHAR(500), -- e.g., /calls/2025-06-11/call_001/recording.opus
    storage_backend VARCHAR(50) NOT NULL,
    is_encrypted BOOLEAN DEFAULT true,
    encryption_key_id VARCHAR(255),
    legacy_call_number INTEGER, -- For original file structure compatibility
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_format CHECK (format IN ('opus', 'mp3', 'wav', 'flac'))
);

-- Call transcriptions with speaker identification
CREATE TABLE call_transcriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_id UUID NOT NULL REFERENCES calls(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    language VARCHAR(10) DEFAULT 'en',
    confidence_score DECIMAL(3,2),
    processing_time_ms INTEGER,
    model_version VARCHAR(50),
    speaker_segments JSONB, -- Array of speaker segments with timestamps
    keywords JSONB, -- Extracted keywords and entities
    sentiment_analysis JSONB, -- Sentiment scores and analysis
    txt_file_path VARCHAR(500), -- e.g., /calls/2025-06-11/call_001/transcription.txt
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Call analytics and metrics (matches original metadata.json structure)
CREATE TABLE call_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_id UUID NOT NULL REFERENCES calls(id) ON DELETE CASCADE,
    duration VARCHAR(20), -- Format: "00:05:23" (matches original spec)
    participants JSONB, -- Array: ["Sales Representative", "Client"]
    talk_time VARCHAR(20), -- Format: "00:03:15" (matches original spec)
    listen_time VARCHAR(20), -- Format: "00:02:08" (matches original spec)
    silence_time_seconds INTEGER,
    interruptions_count INTEGER,
    speaking_pace_wpm INTEGER, -- Words per minute
    sentiment_score DECIMAL(3,2),
    engagement_score DECIMAL(3,2),
    call_quality_score DECIMAL(3,2),
    summary TEXT, -- Matches original: "The sales representative presented product X..."
    action_items JSONB,
    next_steps JSONB,
    metadata_json_path VARCHAR(500), -- e.g., /calls/2025-06-11/call_001/metadata.json
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.4 Storage and Synchronization

```sql
-- Storage backend configurations
CREATE TABLE storage_backends (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    configuration JSONB NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    is_enabled BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 100,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_type CHECK (type IN ('minio', 'google_drive', 'aws_s3', 'azure_blob', 'crm'))
);

-- File synchronization status tracking
CREATE TABLE file_sync_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id UUID NOT NULL, -- References recording or transcription
    file_type VARCHAR(50) NOT NULL,
    backend_id UUID NOT NULL REFERENCES storage_backends(id),
    sync_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    file_path VARCHAR(500),
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_attempts INTEGER DEFAULT 0,
    error_message TEXT,
    checksum VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_file_type CHECK (file_type IN ('recording', 'transcription', 'metadata')),
    CONSTRAINT valid_sync_status CHECK (
        sync_status IN ('pending', 'in_progress', 'completed', 'failed', 'skipped')
    )
);

-- Workflow automation tracking
CREATE TABLE workflow_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_id UUID NOT NULL REFERENCES calls(id),
    workflow_name VARCHAR(255) NOT NULL,
    workflow_version VARCHAR(50),
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    input_data JSONB,
    output_data JSONB,
    error_details JSONB,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT valid_workflow_status CHECK (
        status IN ('pending', 'running', 'completed', 'failed', 'cancelled')
    )
);
```

## 3. Indexing Strategy

### 3.1 Performance Indexes

```sql
-- User and authentication indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_organization ON users(organization_id) WHERE is_active = true;
CREATE INDEX idx_user_sessions_token ON user_sessions(token_hash);
CREATE INDEX idx_user_sessions_user_active ON user_sessions(user_id) WHERE expires_at > NOW();

-- Contact management indexes
CREATE INDEX idx_contacts_organization ON contacts(organization_id);
CREATE INDEX idx_contacts_phone ON contacts(phone);
CREATE INDEX idx_contacts_email ON contacts(email);
CREATE INDEX idx_contacts_company ON contacts(company);
CREATE INDEX idx_contacts_last_contacted ON contacts(last_contacted_at DESC);
CREATE INDEX idx_contacts_tags ON contacts USING GIN(tags);
CREATE INDEX idx_contacts_custom_fields ON contacts USING GIN(custom_fields);

-- Call and recording indexes
CREATE INDEX idx_calls_user_date ON calls(user_id, started_at DESC);
CREATE INDEX idx_calls_contact ON calls(contact_id);
CREATE INDEX idx_calls_organization_date ON calls(organization_id, started_at DESC);
CREATE INDEX idx_calls_status ON calls(status);
CREATE INDEX idx_call_recordings_call ON call_recordings(call_id);
CREATE INDEX idx_call_transcriptions_call ON call_transcriptions(call_id);

-- Full-text search indexes
CREATE INDEX idx_transcriptions_content_fts ON call_transcriptions USING GIN(to_tsvector('english', content));
CREATE INDEX idx_contacts_search_fts ON contacts USING GIN(
    to_tsvector('english', first_name || ' ' || last_name || ' ' || COALESCE(company, ''))
);

-- Storage and sync indexes
CREATE INDEX idx_file_sync_status_file ON file_sync_status(file_id, file_type);
CREATE INDEX idx_file_sync_status_backend ON file_sync_status(backend_id, sync_status);
CREATE INDEX idx_workflow_executions_call ON workflow_executions(call_id);
```

### 3.2 Partitioning Strategy

```sql
-- Partition calls table by month for better performance
CREATE TABLE calls_y2024m01 PARTITION OF calls
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE calls_y2024m02 PARTITION OF calls
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- Partition transcriptions by month
CREATE TABLE call_transcriptions_y2024m01 PARTITION OF call_transcriptions
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Automatic partition creation function
CREATE OR REPLACE FUNCTION create_monthly_partitions()
RETURNS void AS $$
DECLARE
    start_date date;
    end_date date;
    table_name text;
BEGIN
    start_date := date_trunc('month', CURRENT_DATE + interval '1 month');
    end_date := start_date + interval '1 month';
    
    -- Create calls partition
    table_name := 'calls_y' || to_char(start_date, 'YYYY') || 'm' || to_char(start_date, 'MM');
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF calls FOR VALUES FROM (%L) TO (%L)',
                   table_name, start_date, end_date);
    
    -- Create transcriptions partition
    table_name := 'call_transcriptions_y' || to_char(start_date, 'YYYY') || 'm' || to_char(start_date, 'MM');
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF call_transcriptions FOR VALUES FROM (%L) TO (%L)',
                   table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;

-- Schedule monthly partition creation
SELECT cron.schedule('create-partitions', '0 0 1 * *', 'SELECT create_monthly_partitions();');
```

## 4. Data Management Policies

### 4.1 Data Retention and Archival

```sql
-- Data retention policies table
CREATE TABLE data_retention_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    data_type VARCHAR(50) NOT NULL,
    retention_days INTEGER NOT NULL,
    archive_after_days INTEGER,
    delete_after_days INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_data_type CHECK (
        data_type IN ('calls', 'recordings', 'transcriptions', 'analytics', 'logs')
    )
);

-- Automated cleanup function
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
DECLARE
    policy RECORD;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    FOR policy IN SELECT * FROM data_retention_policies WHERE is_active = true LOOP
        cutoff_date := NOW() - (policy.delete_after_days || ' days')::INTERVAL;
        
        CASE policy.data_type
            WHEN 'calls' THEN
                DELETE FROM calls WHERE created_at < cutoff_date;
            WHEN 'recordings' THEN
                DELETE FROM call_recordings WHERE created_at < cutoff_date;
            WHEN 'transcriptions' THEN
                DELETE FROM call_transcriptions WHERE created_at < cutoff_date;
        END CASE;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### 4.2 Data Backup and Recovery

**Backup Strategy:**
- Daily incremental backups
- Weekly full backups
- Monthly archive backups
- Cross-region replication for disaster recovery

**Recovery Procedures:**
- Point-in-time recovery capability
- Automated backup verification
- Recovery time objective (RTO): 4 hours
- Recovery point objective (RPO): 1 hour

### 4.3 Data Security and Encryption

```sql
-- Encryption key management
CREATE TABLE encryption_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_name VARCHAR(255) NOT NULL,
    key_version INTEGER NOT NULL DEFAULT 1,
    algorithm VARCHAR(50) NOT NULL DEFAULT 'AES-256-GCM',
    key_data_encrypted BYTEA NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    
    UNIQUE(key_name, key_version)
);

-- Audit log for data access
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    ip_address INET,
    user_agent TEXT,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 5. Data Migration and Synchronization

### 5.1 CRM Integration Strategy

**Synchronization Patterns:**
- Real-time sync for critical data (contacts, calls)
- Batch sync for historical data
- Conflict resolution based on timestamps
- Bidirectional sync with change tracking

**Data Mapping:**
```sql
-- CRM field mapping configuration
CREATE TABLE crm_field_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    crm_system VARCHAR(50) NOT NULL,
    local_field VARCHAR(100) NOT NULL,
    crm_field VARCHAR(100) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    transformation_rules JSONB,
    is_bidirectional BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5.2 Multi-Client Data Isolation

**Tenant Isolation:**
- Organization-based data partitioning
- Row-level security policies
- Separate encryption keys per organization
- Isolated backup and recovery procedures

```sql
-- Row-level security for multi-tenancy
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
CREATE POLICY contacts_isolation ON contacts
    USING (organization_id = current_setting('app.current_organization_id')::UUID);

ALTER TABLE calls ENABLE ROW LEVEL SECURITY;
CREATE POLICY calls_isolation ON calls
    USING (organization_id = current_setting('app.current_organization_id')::UUID);
```

This comprehensive database schema and data management strategy provides a solid foundation for handling multi-client data with proper isolation, security, and scalability considerations.
