# Sales Dialer Application - Security Implementation Plan

## 1. Security Framework Overview

### 1.1 Security Principles

**Defense in Depth:**
- Multiple layers of security controls
- Fail-safe defaults and least privilege
- Security by design, not as an afterthought
- Continuous monitoring and threat detection

**Compliance Requirements:**
- GDPR (General Data Protection Regulation)
- CCPA (California Consumer Privacy Act)
- SOC 2 Type II compliance
- Industry-specific regulations (TCPA for call recording)

### 1.2 Threat Model

**Primary Threats:**
- Unauthorized access to call recordings and transcriptions
- Data breaches and privacy violations
- Man-in-the-middle attacks on real-time communications
- Injection attacks (SQL, XSS, CSRF)
- Denial of Service (DoS) attacks
- Insider threats and privilege escalation

**Attack Vectors:**
- Web application vulnerabilities
- API security weaknesses
- Container and infrastructure compromises
- Social engineering and phishing
- Third-party service vulnerabilities

## 2. Authentication and Authorization

### 2.1 Multi-Factor Authentication (MFA)

**Implementation Strategy:**
```javascript
// MFA Configuration
const mfaConfig = {
  enabled: true,
  methods: ['totp', 'sms', 'email'],
  gracePeriod: 30, // days before MFA is required
  backupCodes: 10,
  sessionTimeout: 3600 // seconds
};

// TOTP Implementation
const speakeasy = require('speakeasy');

function generateTOTPSecret(user) {
  const secret = speakeasy.generateSecret({
    name: `Sales Dialer (${user.email})`,
    issuer: 'Sales Dialer App',
    length: 32
  });
  
  return {
    secret: secret.base32,
    qrCode: secret.otpauth_url,
    backupCodes: generateBackupCodes()
  };
}

function verifyTOTP(token, secret) {
  return speakeasy.totp.verify({
    secret: secret,
    encoding: 'base32',
    token: token,
    window: 2 // Allow 2 time steps of variance
  });
}
```

**MFA Enforcement Policy:**
- Required for all admin and manager roles
- Optional but recommended for agents
- Backup codes for account recovery
- Device registration and trust management

### 2.2 JWT Token Security

**Token Configuration:**
```javascript
const jwtConfig = {
  algorithm: 'RS256', // RSA with SHA-256
  accessTokenExpiry: '15m',
  refreshTokenExpiry: '7d',
  issuer: 'sales-dialer-app',
  audience: 'sales-dialer-users',
  keyRotationInterval: '30d'
};

// JWT Implementation with key rotation
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class JWTManager {
  constructor() {
    this.keyPairs = new Map();
    this.currentKeyId = null;
    this.rotateKeys();
  }

  rotateKeys() {
    const keyId = crypto.randomUUID();
    const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: { type: 'spki', format: 'pem' },
      privateKeyEncoding: { type: 'pkcs8', format: 'pem' }
    });

    this.keyPairs.set(keyId, { publicKey, privateKey });
    this.currentKeyId = keyId;

    // Schedule next rotation
    setTimeout(() => this.rotateKeys(), 30 * 24 * 60 * 60 * 1000);
  }

  generateToken(payload) {
    const privateKey = this.keyPairs.get(this.currentKeyId).privateKey;
    return jwt.sign(payload, privateKey, {
      algorithm: 'RS256',
      expiresIn: '15m',
      issuer: jwtConfig.issuer,
      audience: jwtConfig.audience,
      keyid: this.currentKeyId
    });
  }

  verifyToken(token) {
    const decoded = jwt.decode(token, { complete: true });
    const keyId = decoded.header.kid;
    const publicKey = this.keyPairs.get(keyId)?.publicKey;

    if (!publicKey) {
      throw new Error('Invalid key ID');
    }

    return jwt.verify(token, publicKey, {
      algorithms: ['RS256'],
      issuer: jwtConfig.issuer,
      audience: jwtConfig.audience
    });
  }
}
```

### 2.3 Role-Based Access Control (RBAC)

**Permission Matrix:**
```javascript
const permissions = {
  admin: [
    'users:*', 'organizations:*', 'calls:*', 'contacts:*',
    'recordings:*', 'transcriptions:*', 'analytics:*', 'settings:*'
  ],
  manager: [
    'users:read', 'calls:*', 'contacts:*', 'recordings:read',
    'transcriptions:read', 'analytics:read', 'reports:*'
  ],
  agent: [
    'calls:create', 'calls:read:own', 'contacts:read', 'contacts:update',
    'recordings:read:own', 'transcriptions:read:own'
  ],
  viewer: [
    'calls:read', 'contacts:read', 'recordings:read', 'transcriptions:read'
  ]
};

// Permission checking middleware
function requirePermission(permission) {
  return (req, res, next) => {
    const userPermissions = permissions[req.user.role] || [];
    
    if (hasPermission(userPermissions, permission, req.user, req.params)) {
      next();
    } else {
      res.status(403).json({ error: 'Insufficient permissions' });
    }
  };
}

function hasPermission(userPermissions, requiredPermission, user, params) {
  return userPermissions.some(perm => {
    if (perm === requiredPermission) return true;
    if (perm.endsWith(':*') && requiredPermission.startsWith(perm.slice(0, -1))) return true;
    if (perm.endsWith(':own') && requiredPermission.startsWith(perm.slice(0, -4))) {
      return params.userId === user.id;
    }
    return false;
  });
}
```

## 3. Data Protection and Encryption

### 3.1 Encryption at Rest

**Database Encryption:**
```sql
-- Enable transparent data encryption
ALTER DATABASE sales_dialer SET encryption = 'AES-256';

-- Encrypt sensitive columns
CREATE TABLE call_recordings (
    id UUID PRIMARY KEY,
    call_id UUID NOT NULL,
    encrypted_content BYTEA NOT NULL,
    encryption_key_id VARCHAR(255) NOT NULL,
    iv BYTEA NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to encrypt sensitive data
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(
    data TEXT,
    key_id VARCHAR(255)
) RETURNS BYTEA AS $$
DECLARE
    encryption_key BYTEA;
    iv BYTEA;
    encrypted_data BYTEA;
BEGIN
    -- Get encryption key from key management service
    SELECT key_data INTO encryption_key FROM encryption_keys WHERE id = key_id;
    
    -- Generate random IV
    iv := gen_random_bytes(16);
    
    -- Encrypt data using AES-256-CBC
    encrypted_data := pgp_sym_encrypt(data, encryption_key, 'cipher-algo=aes256');
    
    RETURN encrypted_data;
END;
$$ LANGUAGE plpgsql;
```

**File System Encryption:**
```javascript
const crypto = require('crypto');
const fs = require('fs').promises;

class FileEncryption {
  constructor(keyManager) {
    this.keyManager = keyManager;
  }

  async encryptFile(filePath, keyId) {
    const key = await this.keyManager.getKey(keyId);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-gcm', key);
    
    const data = await fs.readFile(filePath);
    const encrypted = Buffer.concat([
      cipher.update(data),
      cipher.final()
    ]);
    
    const authTag = cipher.getAuthTag();
    
    // Store IV and auth tag with encrypted data
    const encryptedWithMetadata = Buffer.concat([
      iv,
      authTag,
      encrypted
    ]);
    
    await fs.writeFile(filePath + '.enc', encryptedWithMetadata);
    await fs.unlink(filePath); // Remove original file
    
    return {
      encryptedPath: filePath + '.enc',
      keyId: keyId,
      iv: iv.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  async decryptFile(encryptedPath, keyId) {
    const key = await this.keyManager.getKey(keyId);
    const encryptedData = await fs.readFile(encryptedPath);
    
    const iv = encryptedData.slice(0, 16);
    const authTag = encryptedData.slice(16, 32);
    const encrypted = encryptedData.slice(32);
    
    const decipher = crypto.createDecipher('aes-256-gcm', key);
    decipher.setAuthTag(authTag);
    
    const decrypted = Buffer.concat([
      decipher.update(encrypted),
      decipher.final()
    ]);
    
    return decrypted;
  }
}
```

### 3.2 Encryption in Transit

**TLS Configuration:**
```nginx
# Nginx SSL/TLS configuration
server {
    listen 443 ssl http2;
    server_name api.salesdialer.com;

    # SSL certificates
    ssl_certificate /etc/ssl/certs/salesdialer.crt;
    ssl_certificate_key /etc/ssl/private/salesdialer.key;

    # SSL security settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'";

    location / {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**WebRTC Security:**
```javascript
// Secure WebRTC configuration
const rtcConfig = {
  iceServers: [
    {
      urls: ['stun:stun.l.google.com:19302'],
    },
    {
      urls: ['turn:turn.salesdialer.com:3478'],
      username: 'turnuser',
      credential: 'turnpass',
      credentialType: 'password'
    }
  ],
  iceCandidatePoolSize: 10,
  bundlePolicy: 'max-bundle',
  rtcpMuxPolicy: 'require',
  sdpSemantics: 'unified-plan'
};

// DTLS-SRTP for media encryption
const mediaConstraints = {
  audio: {
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
    sampleRate: 48000,
    channelCount: 1
  },
  video: false
};
```

## 4. Input Validation and Sanitization

### 4.1 API Input Validation

**Validation Schema:**
```javascript
const Joi = require('joi');

const schemas = {
  createContact: Joi.object({
    first_name: Joi.string().min(1).max(100).required(),
    last_name: Joi.string().min(1).max(100).required(),
    email: Joi.string().email().optional(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    company: Joi.string().max(255).optional(),
    custom_fields: Joi.object().pattern(
      Joi.string(),
      Joi.alternatives().try(Joi.string(), Joi.number(), Joi.boolean())
    ).optional()
  }),

  initiateCall: Joi.object({
    contact_id: Joi.string().uuid().optional(),
    phone_number: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
    recording_enabled: Joi.boolean().default(true),
    transcription_enabled: Joi.boolean().default(true)
  })
};

// Validation middleware
function validateInput(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: error.details[0].message,
          details: error.details
        }
      });
    }
    req.body = value;
    next();
  };
}
```

### 4.2 SQL Injection Prevention

**Parameterized Queries:**
```javascript
// Using parameterized queries with pg
const { Pool } = require('pg');

class DatabaseManager {
  constructor(config) {
    this.pool = new Pool(config);
  }

  async getContacts(organizationId, filters = {}) {
    const conditions = ['organization_id = $1'];
    const params = [organizationId];
    let paramIndex = 2;

    if (filters.search) {
      conditions.push(`(first_name ILIKE $${paramIndex} OR last_name ILIKE $${paramIndex} OR company ILIKE $${paramIndex})`);
      params.push(`%${filters.search}%`);
      paramIndex++;
    }

    if (filters.tags && filters.tags.length > 0) {
      conditions.push(`tags && $${paramIndex}`);
      params.push(filters.tags);
      paramIndex++;
    }

    const query = `
      SELECT id, first_name, last_name, company, email, phone, tags, created_at
      FROM contacts
      WHERE ${conditions.join(' AND ')}
      ORDER BY last_name, first_name
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    params.push(filters.limit || 50, filters.offset || 0);

    const result = await this.pool.query(query, params);
    return result.rows;
  }
}
```

## 5. Security Monitoring and Incident Response

### 5.1 Security Event Logging

**Audit Logging:**
```javascript
const winston = require('winston');

const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'security-audit.log' }),
    new winston.transports.Console()
  ]
});

function logSecurityEvent(event) {
  securityLogger.info({
    event_type: event.type,
    user_id: event.userId,
    ip_address: event.ipAddress,
    user_agent: event.userAgent,
    resource: event.resource,
    action: event.action,
    success: event.success,
    risk_score: event.riskScore,
    timestamp: new Date().toISOString()
  });
}

// Security event types
const SECURITY_EVENTS = {
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILURE: 'login_failure',
  MFA_CHALLENGE: 'mfa_challenge',
  PERMISSION_DENIED: 'permission_denied',
  DATA_ACCESS: 'data_access',
  DATA_EXPORT: 'data_export',
  SUSPICIOUS_ACTIVITY: 'suspicious_activity'
};
```

### 5.2 Intrusion Detection

**Rate Limiting and Anomaly Detection:**
```javascript
const Redis = require('redis');
const redis = Redis.createClient();

class SecurityMonitor {
  constructor() {
    this.thresholds = {
      loginAttempts: { limit: 5, window: 300 }, // 5 attempts in 5 minutes
      apiCalls: { limit: 1000, window: 3600 }, // 1000 calls per hour
      dataDownloads: { limit: 10, window: 3600 } // 10 downloads per hour
    };
  }

  async checkRateLimit(key, threshold) {
    const current = await redis.incr(key);
    if (current === 1) {
      await redis.expire(key, threshold.window);
    }

    if (current > threshold.limit) {
      await this.triggerSecurityAlert({
        type: 'RATE_LIMIT_EXCEEDED',
        key: key,
        current: current,
        limit: threshold.limit
      });
      return false;
    }

    return true;
  }

  async detectAnomalies(userId, action) {
    const patterns = await this.getUserPatterns(userId);
    const currentBehavior = await this.getCurrentBehavior(userId, action);

    const anomalyScore = this.calculateAnomalyScore(patterns, currentBehavior);

    if (anomalyScore > 0.8) {
      await this.triggerSecurityAlert({
        type: 'ANOMALOUS_BEHAVIOR',
        userId: userId,
        action: action,
        anomalyScore: anomalyScore
      });
    }

    return anomalyScore;
  }

  async triggerSecurityAlert(alert) {
    // Log the alert
    logSecurityEvent({
      type: 'SECURITY_ALERT',
      ...alert
    });

    // Send notification to security team
    await this.notifySecurityTeam(alert);

    // Take automated response if necessary
    if (alert.anomalyScore > 0.9) {
      await this.suspendUser(alert.userId);
    }
  }
}
```

## 6. Compliance and Privacy

### 6.1 GDPR Compliance

**Data Subject Rights Implementation:**
```javascript
class GDPRCompliance {
  async handleDataSubjectRequest(type, userId, requestData) {
    switch (type) {
      case 'ACCESS':
        return await this.exportUserData(userId);
      case 'RECTIFICATION':
        return await this.updateUserData(userId, requestData);
      case 'ERASURE':
        return await this.deleteUserData(userId);
      case 'PORTABILITY':
        return await this.exportUserDataPortable(userId);
      case 'RESTRICTION':
        return await this.restrictUserDataProcessing(userId);
      default:
        throw new Error('Invalid request type');
    }
  }

  async exportUserData(userId) {
    const userData = {
      profile: await this.getUserProfile(userId),
      calls: await this.getUserCalls(userId),
      recordings: await this.getUserRecordings(userId),
      transcriptions: await this.getUserTranscriptions(userId)
    };

    // Log the data export
    logSecurityEvent({
      type: SECURITY_EVENTS.DATA_EXPORT,
      userId: userId,
      dataTypes: Object.keys(userData)
    });

    return userData;
  }

  async deleteUserData(userId) {
    // Soft delete with retention for legal requirements
    await this.pool.query(
      'UPDATE users SET deleted_at = NOW(), email = $1 WHERE id = $2',
      [`deleted_${Date.now()}@example.com`, userId]
    );

    // Schedule hard delete after retention period
    await this.scheduleHardDelete(userId, 90); // 90 days retention
  }
}
```

This security implementation plan provides comprehensive protection for the Sales Dialer Application, covering authentication, encryption, monitoring, and compliance requirements.
