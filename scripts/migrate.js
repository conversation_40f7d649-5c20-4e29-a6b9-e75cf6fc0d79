#!/usr/bin/env node

/**
 * Database migration script for Sales Dialer Application
 * Runs all pending migrations in order
 */

require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');
const { Pool } = require('pg');

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`)
};

class MigrationRunner {
  constructor() {
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
  }

  async initialize() {
    try {
      // Create migrations table if it doesn't exist
      await this.pool.query(`
        CREATE TABLE IF NOT EXISTS migrations (
          id SERIAL PRIMARY KEY,
          filename VARCHAR(255) NOT NULL UNIQUE,
          executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          checksum VARCHAR(64)
        );
      `);
      
      logger.info('Migrations table initialized');
    } catch (error) {
      logger.error(`Failed to initialize migrations table: ${error.message}`);
      throw error;
    }
  }

  async getExecutedMigrations() {
    try {
      const result = await this.pool.query(
        'SELECT filename FROM migrations ORDER BY id'
      );
      return result.rows.map(row => row.filename);
    } catch (error) {
      logger.error(`Failed to get executed migrations: ${error.message}`);
      throw error;
    }
  }

  async getMigrationFiles() {
    try {
      const migrationsDir = path.join(__dirname, '../database/migrations');
      const files = await fs.readdir(migrationsDir);
      
      return files
        .filter(file => file.endsWith('.sql'))
        .sort()
        .map(file => ({
          filename: file,
          path: path.join(migrationsDir, file)
        }));
    } catch (error) {
      logger.error(`Failed to read migration files: ${error.message}`);
      throw error;
    }
  }

  async calculateChecksum(content) {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  async executeMigration(migration) {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Read migration file
      const content = await fs.readFile(migration.path, 'utf8');
      const checksum = await this.calculateChecksum(content);
      
      logger.info(`Executing migration: ${migration.filename}`);
      
      // Execute migration SQL
      await client.query(content);
      
      // Record migration as executed
      await client.query(
        'INSERT INTO migrations (filename, checksum) VALUES ($1, $2)',
        [migration.filename, checksum]
      );
      
      await client.query('COMMIT');
      
      logger.info(`Migration completed: ${migration.filename}`);
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error(`Migration failed: ${migration.filename} - ${error.message}`);
      throw error;
    } finally {
      client.release();
    }
  }

  async run() {
    try {
      logger.info('Starting database migration...');
      
      await this.initialize();
      
      const executedMigrations = await this.getExecutedMigrations();
      const migrationFiles = await this.getMigrationFiles();
      
      logger.info(`Found ${migrationFiles.length} migration files`);
      logger.info(`${executedMigrations.length} migrations already executed`);
      
      const pendingMigrations = migrationFiles.filter(
        migration => !executedMigrations.includes(migration.filename)
      );
      
      if (pendingMigrations.length === 0) {
        logger.info('No pending migrations');
        return;
      }
      
      logger.info(`Executing ${pendingMigrations.length} pending migrations`);
      
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration);
      }
      
      logger.info('All migrations completed successfully');
    } catch (error) {
      logger.error(`Migration failed: ${error.message}`);
      process.exit(1);
    } finally {
      await this.pool.end();
    }
  }

  async rollback(steps = 1) {
    try {
      logger.info(`Rolling back ${steps} migration(s)...`);
      
      const result = await this.pool.query(
        'SELECT filename FROM migrations ORDER BY id DESC LIMIT $1',
        [steps]
      );
      
      if (result.rows.length === 0) {
        logger.info('No migrations to rollback');
        return;
      }
      
      for (const row of result.rows) {
        logger.warn(`Rolling back migration: ${row.filename}`);
        
        // Remove from migrations table
        await this.pool.query(
          'DELETE FROM migrations WHERE filename = $1',
          [row.filename]
        );
        
        logger.warn(`Rollback completed: ${row.filename}`);
      }
      
      logger.info('Rollback completed successfully');
      logger.warn('Note: SQL rollback must be done manually if needed');
    } catch (error) {
      logger.error(`Rollback failed: ${error.message}`);
      process.exit(1);
    } finally {
      await this.pool.end();
    }
  }

  async status() {
    try {
      const executedMigrations = await this.getExecutedMigrations();
      const migrationFiles = await this.getMigrationFiles();
      
      console.log('\n=== Migration Status ===\n');
      
      for (const migration of migrationFiles) {
        const isExecuted = executedMigrations.includes(migration.filename);
        const status = isExecuted ? '✓ EXECUTED' : '✗ PENDING';
        console.log(`${status} - ${migration.filename}`);
      }
      
      console.log(`\nTotal: ${migrationFiles.length} migrations`);
      console.log(`Executed: ${executedMigrations.length}`);
      console.log(`Pending: ${migrationFiles.length - executedMigrations.length}\n`);
    } catch (error) {
      logger.error(`Failed to get migration status: ${error.message}`);
      process.exit(1);
    } finally {
      await this.pool.end();
    }
  }
}

// CLI interface
async function main() {
  const command = process.argv[2];
  const runner = new MigrationRunner();
  
  switch (command) {
    case 'up':
    case 'migrate':
      await runner.run();
      break;
      
    case 'down':
    case 'rollback':
      const steps = parseInt(process.argv[3]) || 1;
      await runner.rollback(steps);
      break;
      
    case 'status':
      await runner.status();
      break;
      
    default:
      console.log(`
Usage: node migrate.js <command>

Commands:
  up, migrate     Run all pending migrations
  down, rollback  Rollback last migration (or specify number of steps)
  status          Show migration status

Examples:
  node migrate.js up
  node migrate.js down
  node migrate.js down 2
  node migrate.js status
      `);
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    logger.error(`Migration script failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = MigrationRunner;
