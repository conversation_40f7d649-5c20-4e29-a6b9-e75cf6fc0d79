#!/bin/bash

# Sales Dialer Application - Development Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Sales Dialer Application Development Environment"
echo "============================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose (v1 or v2)
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_warning "Node.js is not installed. You'll need it for local development."
    else
        NODE_VERSION=$(node --version)
        print_success "Node.js version: $NODE_VERSION"
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_warning "npm is not installed. You'll need it for local development."
    else
        NPM_VERSION=$(npm --version)
        print_success "npm version: $NPM_VERSION"
    fi
    
    print_success "Requirements check completed"
}

# Create environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        print_status "Creating .env file from .env.example..."
        cp .env.example .env
        print_success ".env file created"
        print_warning "Please review and update the .env file with your actual configuration"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "calls"
        "temp"
        "logs"
        "models"
        "uploads"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        else
            print_warning "Directory already exists: $dir"
        fi
    done
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Frontend dependencies
    if [ -d "frontend" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
        print_success "Frontend dependencies installed"
    fi
    
    # Backend service dependencies
    services=(
        "backend/api-gateway"
        "backend/auth-service"
        "backend/contact-service"
        "backend/telephony-service"
        "backend/storage-service"
    )
    
    for service in "${services[@]}"; do
        if [ -d "$service" ] && [ -f "$service/package.json" ]; then
            print_status "Installing dependencies for $service..."
            cd "$service"
            npm install
            cd - > /dev/null
            print_success "Dependencies installed for $service"
        fi
    done
}

# Setup Docker environment
setup_docker() {
    print_status "Setting up Docker environment..."
    
    # Pull required images
    print_status "Pulling Docker images..."
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.dev.yml pull postgres redis minio n8n
    else
        docker compose -f docker-compose.dev.yml pull postgres redis minio n8n
    fi
    
    print_success "Docker images pulled"
}

# Start services
start_services() {
    print_status "Starting development services..."
    
    # Start infrastructure services first
    print_status "Starting infrastructure services (PostgreSQL, Redis, MinIO, n8n)..."
    if command -v docker-compose &> /dev/null; then
        docker-compose -f docker-compose.dev.yml up -d postgres redis minio n8n
    else
        docker compose -f docker-compose.dev.yml up -d postgres redis minio n8n
    fi
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are healthy
    print_status "Checking service health..."
    
    # Check PostgreSQL
    if command -v docker-compose &> /dev/null; then
        if docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
            print_success "PostgreSQL is ready"
        else
            print_warning "PostgreSQL might not be ready yet"
        fi

        # Check Redis
        if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
            print_success "Redis is ready"
        else
            print_warning "Redis might not be ready yet"
        fi
    else
        if docker compose -f docker-compose.dev.yml exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
            print_success "PostgreSQL is ready"
        else
            print_warning "PostgreSQL might not be ready yet"
        fi

        # Check Redis
        if docker compose -f docker-compose.dev.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
            print_success "Redis is ready"
        else
            print_warning "Redis might not be ready yet"
        fi
    fi
    
    print_success "Infrastructure services started"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Wait a bit more for PostgreSQL to be fully ready
    sleep 5
    
    # Run migrations
    if node scripts/migrate.js up; then
        print_success "Database migrations completed"
    else
        print_error "Database migrations failed"
        return 1
    fi
}

# Seed development data
seed_data() {
    print_status "Seeding development data..."
    
    # Check if seed file exists
    if [ -f "database/seeds/001_development_data.sql" ]; then
        # Run seed data
        if command -v docker-compose &> /dev/null; then
            if docker-compose -f docker-compose.dev.yml exec -T postgres psql -U postgres -d salesdialer -f /docker-entrypoint-initdb.d/../seeds/001_development_data.sql > /dev/null 2>&1; then
                print_success "Development data seeded"
            else
                print_warning "Failed to seed development data (this might be normal if data already exists)"
            fi
        else
            if docker compose -f docker-compose.dev.yml exec -T postgres psql -U postgres -d salesdialer -f /docker-entrypoint-initdb.d/../seeds/001_development_data.sql > /dev/null 2>&1; then
                print_success "Development data seeded"
            else
                print_warning "Failed to seed development data (this might be normal if data already exists)"
            fi
        fi
    else
        print_warning "No seed data file found"
    fi
}

# Display final information
show_info() {
    echo ""
    echo "🎉 Development environment setup completed!"
    echo "=========================================="
    echo ""
    echo "Services are running on the following ports:"
    echo "  • Frontend (when started):     http://localhost:3000"
    echo "  • API Gateway (when started):  http://localhost:3001"
    echo "  • PostgreSQL:                  localhost:5432"
    echo "  • Redis:                       localhost:6379"
    echo "  • MinIO Console:               http://localhost:9001"
    echo "  • n8n Workflows:               http://localhost:5678"
    echo ""
    echo "To start the application services:"
    if command -v docker-compose &> /dev/null; then
        echo "  docker-compose -f docker-compose.dev.yml up -d"
        echo ""
        echo "To view logs:"
        echo "  docker-compose -f docker-compose.dev.yml logs -f [service-name]"
        echo ""
        echo "To stop all services:"
        echo "  docker-compose -f docker-compose.dev.yml down"
    else
        echo "  docker compose -f docker-compose.dev.yml up -d"
        echo ""
        echo "To view logs:"
        echo "  docker compose -f docker-compose.dev.yml logs -f [service-name]"
        echo ""
        echo "To stop all services:"
        echo "  docker compose -f docker-compose.dev.yml down"
    fi
    echo ""
    echo "Default credentials:"
    echo "  • MinIO: minioadmin / minioadmin123"
    echo "  • n8n: admin / admin123"
    echo "  • Database: postgres / password"
    echo ""
    print_warning "Remember to update your .env file with actual API keys and secrets!"
}

# Main execution
main() {
    check_requirements
    setup_environment
    create_directories
    install_dependencies
    setup_docker
    start_services
    run_migrations
    seed_data
    show_info
}

# Handle script interruption
trap 'print_error "Setup interrupted"; exit 1' INT TERM

# Run main function
main

print_success "Setup completed successfully! 🚀"
