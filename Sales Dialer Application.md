## Description

The Sales Dialer Application is a tool designed for sales representatives to make calls, record them, transcribe in real-time, and store the information in various locations such as Google Drive, MinIO S3, or directly in the CRM application. The application runs in a Docker container on a Linux virtual machine.

## Features

- Contact book displaying name, company, and phone number.
- Telephone keypad for dialing extensions.
- Call control buttons: Pause, Hang up, etc.
- Real-time transcription display.
- Call recording in Opus format.
- Metadata and summary generation.
- Storage in Google Drive, MinIO S3, or CRM application.
- Additional processing with n8n.
- Data sending to an API via webhook.

## Components

### Frontend

- **User Interface:**
  - Contact book.
  - Telephone keypad.
  - Control buttons.
  - Real-time transcription display.

- **Technologies:**
  - React.js or Vue.js for the user interface.
  - Bootstrap or Material-UI for design.

### Backend

- **Application Server:**
  - Business logic management.
  - Integration with the contacts API.
  - Call control and recording.

- **Technologies:**
  - Node.js with Express or FastAPI in Python.
  - Docker for containerization.

- **Telephony Service:**
  - Twilio or VoIP.ms for obtaining numbers and making calls.

- **Recording and Transcription Service:**
  - Call recording in Opus format.
  - Real-time transcription using Whisper or Mozilla DeepSpeech.

- **Storage and Processing:**
  - Storage of recordings and transcriptions in Google Drive, MinIO S3, or CRM application.
  - Additional processing with n8n.
  - Sending data to an API via webhook.

- **Technologies:**
  - MinIO for S3-compatible storage.
  - Rclone for synchronization with Google Drive.
  - n8n for workflow automation.

## Workflow

### Login and Configuration

- The sales representative logs into the application.
- The application loads the contact book from the CRM application API.

### Making Calls

- The sales representative selects a contact and makes the call.
- The application uses Twilio or VoIP.ms to establish the call.
- Call recording starts automatically.

### Recording and Transcription

- The call is recorded in Opus format.
- Whisper or Mozilla DeepSpeech transcribes the call in real-time.
- The transcription is displayed in the user interface.

### Ending the Call

- At the end of the call, the transcription is saved in a TXT file.
- Metadata such as duration, participants, talk time vs. active listening time, etc., is added.
- A call summary with relevant points is generated.

### Storage and Processing

- The transcription file and metadata are stored in a folder synchronized with Google Drive, MinIO S3, or the CRM application.
- An n8n script processes the files and performs additional operations.
- Processed data is sent to an API via webhook.

## Technical Details

### Recording Format

- **Format:** Opus (lightweight and good quality).
- **Storage:** Google Drive, MinIO S3, or CRM application.

### Metadata

- Call duration.
- Participants.
- Talk time vs. active listening time.
- Call summary.

### Transcription

- **Tool:** Whisper or Mozilla DeepSpeech.
- **Output Format:** TXT.

### Additional Processing

- **Tool:** n8n.
- **Operations:** Count of daily and monthly calls, call results, etc.

### API Integration

- **Webhook:** Sending processed data to an API.

## File Structure Example

```plaintext
/calls
  /2025-06-11
    /call_001
      - recording.opus
      - transcription.txt
      - metadata.json
    /call_002
      - recording.opus
      - transcription.txt
      - metadata.json
```

## File Content Examples

### transcription.txt

```plaintext
[00:00:00] Sales Representative: Hello, how are you?
[00:00:02] Client: Fine, thank you. How can I help you?
...
```

### metadata.json

```json
{
  "duration": "00:05:23",
  "participants": ["Sales Representative", "Client"],
  "talk_time": "00:03:15",
  "listen_time": "00:02:08",
  "summary": "The sales representative presented product X and the client showed interest in a demonstration."
}
```

## Docker Implementation

### Dockerfile

```Dockerfile
# Base image
FROM node:14

# Install dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Python dependencies
RUN pip3 install whisper
RUN pip3 install deepspeech

# Install Node.js dependencies
COPY package*.json ./
RUN npm install

# Copy application files
COPY . .

# Expose the application port
EXPOSE 3000

# Command to run the application
CMD ["npm", "start"]
```

### docker-compose.yml

```yaml
version: '3'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./calls:/app/calls
    environment:
      - TWILIO_ACCOUNT_SID=your_twilio_account_sid
      - TWILIO_AUTH_TOKEN=your_twilio_auth_token
      - GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id
      - MINIO_ENDPOINT=your_minio_endpoint
      - MINIO_ACCESS_KEY=your_minio_access_key
      - MINIO_SECRET_KEY=your_minio_secret_key
      - CRM_API_URL=your_crm_api_url

  minio:
    image: minio/minio
    ports:
      - "9000:9000"
    environment:
      - MINIO_ACCESS_KEY=your_minio_access_key
      - MINIO_SECRET_KEY=your_minio_secret_key
    command: server /data

  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n

volumes:
  n8n_data:
```

## Additional Considerations

- **Security:** Ensure that recordings and transcriptions are stored securely and comply with privacy regulations.
- **Scalability:** Design the application to handle a large volume of calls and data.
- **Usability:** The user interface should be intuitive and easy to use for sales representatives.
- **Monitoring:** Implement monitoring tools such as Prometheus and Grafana to monitor the application's performance.
```

