-- Development seed data for Sales Dialer Application
-- This file contains sample data for development and testing

-- Insert development organization
INSERT INTO organizations (id, name, domain, subscription_plan, settings) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440000',
    'Acme Sales Corp',
    'acme.salesdialer.com',
    'enterprise',
    '{"features": ["transcription", "analytics", "crm_integration"], "max_users": 100}'
) ON CONFLICT (id) DO NOTHING;

-- Insert development users
INSERT INTO users (id, email, password_hash, first_name, last_name, role, organization_id) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440001',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlJO', -- password: admin123
    '<PERSON>',
    'Admin',
    'admin',
    '550e8400-e29b-41d4-a716-446655440000'
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlJO', -- password: manager123
    'Jane',
    'Manager',
    'manager',
    '550e8400-e29b-41d4-a716-446655440000'
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlJO', -- password: agent123
    'Bob',
    'Agent',
    'agent',
    '550e8400-e29b-41d4-a716-446655440000'
) ON CONFLICT (email) DO NOTHING;

-- Insert sample contacts
INSERT INTO contacts (id, organization_id, first_name, last_name, company, title, email, phone, tags, created_by) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440000',
    'Alice',
    'Johnson',
    'Tech Solutions Inc',
    'CTO',
    '<EMAIL>',
    '******-0101',
    ARRAY['prospect', 'high-value', 'technology'],
    '550e8400-e29b-41d4-a716-446655440003'
),
(
    '550e8400-e29b-41d4-a716-446655440011',
    '550e8400-e29b-41d4-a716-446655440000',
    'Charlie',
    'Brown',
    'Marketing Masters',
    'VP Marketing',
    '<EMAIL>',
    '******-0102',
    ARRAY['prospect', 'marketing'],
    '550e8400-e29b-41d4-a716-446655440003'
),
(
    '550e8400-e29b-41d4-a716-446655440012',
    '550e8400-e29b-41d4-a716-446655440000',
    'Diana',
    'Smith',
    'Global Enterprises',
    'CEO',
    '<EMAIL>',
    '******-0103',
    ARRAY['client', 'enterprise', 'high-value'],
    '550e8400-e29b-41d4-a716-446655440003'
),
(
    '550e8400-e29b-41d4-a716-446655440013',
    '550e8400-e29b-41d4-a716-446655440000',
    'Edward',
    'Wilson',
    'Startup Innovations',
    'Founder',
    '<EMAIL>',
    '******-0104',
    ARRAY['prospect', 'startup'],
    '550e8400-e29b-41d4-a716-446655440003'
) ON CONFLICT (id) DO NOTHING;

-- Insert sample contact groups
INSERT INTO contact_groups (id, organization_id, name, description, created_by) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440020',
    '550e8400-e29b-41d4-a716-446655440000',
    'High Value Prospects',
    'Contacts with high potential value for the organization',
    '550e8400-e29b-41d4-a716-446655440002'
),
(
    '550e8400-e29b-41d4-a716-446655440021',
    '550e8400-e29b-41d4-a716-446655440000',
    'Technology Sector',
    'Contacts from technology companies',
    '550e8400-e29b-41d4-a716-446655440002'
) ON CONFLICT (id) DO NOTHING;

-- Insert contact group memberships
INSERT INTO contact_group_members (contact_id, group_id, added_by) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440020',
    '550e8400-e29b-41d4-a716-446655440002'
),
(
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440021',
    '550e8400-e29b-41d4-a716-446655440002'
),
(
    '550e8400-e29b-41d4-a716-446655440012',
    '550e8400-e29b-41d4-a716-446655440020',
    '550e8400-e29b-41d4-a716-446655440002'
) ON CONFLICT (contact_id, group_id) DO NOTHING;

-- Insert sample storage backends
INSERT INTO storage_backends (id, organization_id, name, type, configuration, is_primary, is_enabled) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440030',
    '550e8400-e29b-41d4-a716-446655440000',
    'Primary MinIO Storage',
    'minio',
    '{"endpoint": "minio:9000", "bucket": "sales-dialer-recordings", "use_ssl": false}',
    true,
    true
),
(
    '550e8400-e29b-41d4-a716-446655440031',
    '550e8400-e29b-41d4-a716-446655440000',
    'Google Drive Backup',
    'google_drive',
    '{"folder_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"}',
    false,
    true
) ON CONFLICT (id) DO NOTHING;

-- Insert sample calls for testing
INSERT INTO calls (id, organization_id, user_id, contact_id, phone_number, direction, status, provider, started_at, ended_at, duration_seconds) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440040',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440010',
    '******-0101',
    'outbound',
    'completed',
    'twilio',
    NOW() - INTERVAL '2 hours',
    NOW() - INTERVAL '2 hours' + INTERVAL '5 minutes 23 seconds',
    323
),
(
    '550e8400-e29b-41d4-a716-446655440041',
    '550e8400-e29b-41d4-a716-446655440000',
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440011',
    '******-0102',
    'outbound',
    'completed',
    'twilio',
    NOW() - INTERVAL '1 hour',
    NOW() - INTERVAL '1 hour' + INTERVAL '3 minutes 45 seconds',
    225
) ON CONFLICT (id) DO NOTHING;

-- Insert sample contact interactions
INSERT INTO contact_interactions (contact_id, user_id, interaction_type, subject, notes, outcome) VALUES 
(
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440003',
    'call',
    'Initial product demo',
    'Discussed our new CRM integration features. Client showed strong interest in the analytics dashboard.',
    'positive'
),
(
    '550e8400-e29b-41d4-a716-446655440011',
    '550e8400-e29b-41d4-a716-446655440003',
    'call',
    'Follow-up on proposal',
    'Reviewed the pricing proposal. Client requested some modifications to the package.',
    'follow_up_needed'
);

-- Set the current organization for RLS
SELECT set_config('app.current_organization_id', '550e8400-e29b-41d4-a716-446655440000', false);
