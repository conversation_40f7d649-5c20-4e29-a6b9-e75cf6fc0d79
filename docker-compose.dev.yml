version: '3.8'

services:
  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:3001
      - REACT_APP_WS_URL=ws://localhost:3001
      - CHOKIDAR_USEPOLLING=true
    # depends_on:
    #   - api-gateway
    restart: unless-stopped

  # API Gateway Service
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3000"
    volumes:
      - ./backend/api-gateway/src:/app/src
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=dev-secret-key-change-in-production
      - JWT_EXPIRES_IN=15m
      - REFRESH_TOKEN_EXPIRES_IN=7d
      - DATABASE_URL=********************************************/salesdialer
      - REDIS_URL=redis://redis:6379
      # - AUTH_SERVICE_URL=http://auth-service:3000
      - CONTACT_SERVICE_URL=http://contact-service:3000
      - TELEPHONY_SERVICE_URL=http://telephony-service:3000
    depends_on:
      - postgres
      - redis
      # - auth-service
    restart: unless-stopped

  # Authentication Service (temporarily disabled until implementation is ready)
  # auth-service:
  #   build:
  #     context: ./backend/auth-service
  #     dockerfile: Dockerfile.dev
  #   ports:
  #     - "3002:3000"
  #   volumes:
  #     - ./backend/auth-service/src:/app/src
  #     - /app/node_modules
  #   environment:
  #     - NODE_ENV=development
  #     - PORT=3000
  #     - JWT_SECRET=dev-secret-key-change-in-production
  #     - JWT_EXPIRES_IN=15m
  #     - REFRESH_TOKEN_EXPIRES_IN=7d
  #     - DATABASE_URL=********************************************/salesdialer
  #     - REDIS_URL=redis://redis:6379
  #     - BCRYPT_ROUNDS=10
  #   depends_on:
  #     - postgres
  #     - redis
  #   restart: unless-stopped

  # Contact Service
  contact-service:
    build:
      context: ./backend/contact-service
      dockerfile: Dockerfile.dev
    ports:
      - "3003:3000"
    volumes:
      - ./backend/contact-service/src:/app/src
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/salesdialer
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Telephony Service
  telephony-service:
    build:
      context: ./backend/telephony-service
      dockerfile: Dockerfile.dev
    ports:
      - "3004:3000"
    volumes:
      - ./backend/telephony-service/src:/app/src
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/salesdialer
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID:-your_twilio_account_sid}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN:-your_twilio_auth_token}
      - VOIPMS_API_USERNAME=${VOIPMS_API_USERNAME:-your_voipms_username}
      - VOIPMS_API_PASSWORD=${VOIPMS_API_PASSWORD:-your_voipms_password}
    depends_on:
      - postgres
    restart: unless-stopped

  # Transcription Service (Python/FastAPI)
  transcription-service:
    build:
      context: ./backend/transcription-service
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend/transcription-service/src:/app/src
      - ./models:/app/models
      - ./temp:/app/temp
    environment:
      - ENVIRONMENT=development
      - PORT=8000
      - DATABASE_URL=********************************************/salesdialer
      - REDIS_URL=redis://redis:6379
      - MODEL_PATH=/app/models/whisper-base
      - GPU_ENABLED=false
      - MAX_WORKERS=2
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # Storage Service
  storage-service:
    build:
      context: ./backend/storage-service
      dockerfile: Dockerfile.dev
    ports:
      - "3005:3000"
    volumes:
      - ./backend/storage-service/src:/app/src
      - ./calls:/app/calls
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/salesdialer
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - MINIO_USE_SSL=false
      - GOOGLE_DRIVE_FOLDER_ID=${GOOGLE_DRIVE_FOLDER_ID:-}
      - CRM_API_URL=${CRM_API_URL:-}
    depends_on:
      - postgres
      - minio
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=salesdialer
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # MinIO Storage
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped

  # n8n Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - WEBHOOK_URL=http://localhost:5678
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data:
  n8n_data:

networks:
  default:
    driver: bridge
