/**
 * Test setup file for API Gateway
 * Configures the test environment and provides common utilities
 */

const { Pool } = require('pg');
const redis = require('redis');

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret-key';
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/test_db';
process.env.REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';

// Global test timeout
jest.setTimeout(30000);

// Database pool for tests
let testDbPool;

// Redis client for tests
let testRedisClient;

// Setup before all tests
beforeAll(async () => {
  // Initialize test database pool
  testDbPool = new Pool({
    connectionString: process.env.DATABASE_URL,
    max: 5,
  });

  // Initialize test Redis client
  testRedisClient = redis.createClient({
    url: process.env.REDIS_URL,
  });

  try {
    await testRedisClient.connect();
  } catch (error) {
    console.warn('Redis not available for tests:', error.message);
  }

  // Run database migrations for tests
  try {
    await testDbPool.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    `);
  } catch (error) {
    console.warn('Could not create extensions:', error.message);
  }
});

// Cleanup after all tests
afterAll(async () => {
  if (testDbPool) {
    await testDbPool.end();
  }

  if (testRedisClient && testRedisClient.isOpen) {
    await testRedisClient.quit();
  }
});

// Clean up between tests
beforeEach(async () => {
  // Clear Redis cache
  if (testRedisClient && testRedisClient.isOpen) {
    try {
      await testRedisClient.flushDb();
    } catch (error) {
      console.warn('Could not flush Redis:', error.message);
    }
  }
});

// Global test utilities
global.testUtils = {
  // Database utilities
  db: {
    query: async (text, params) => {
      if (!testDbPool) {
        throw new Error('Test database pool not initialized');
      }
      return testDbPool.query(text, params);
    },
    
    getClient: async () => {
      if (!testDbPool) {
        throw new Error('Test database pool not initialized');
      }
      return testDbPool.connect();
    }
  },

  // Redis utilities
  redis: {
    get: async (key) => {
      if (!testRedisClient || !testRedisClient.isOpen) {
        return null;
      }
      return testRedisClient.get(key);
    },
    
    set: async (key, value, options) => {
      if (!testRedisClient || !testRedisClient.isOpen) {
        return null;
      }
      return testRedisClient.set(key, value, options);
    },
    
    del: async (key) => {
      if (!testRedisClient || !testRedisClient.isOpen) {
        return null;
      }
      return testRedisClient.del(key);
    }
  },

  // JWT utilities
  jwt: {
    generateToken: (payload) => {
      const jwt = require('jsonwebtoken');
      return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '1h' });
    },
    
    generateTestUser: () => ({
      sub: 'test-user-id',
      email: '<EMAIL>',
      role: 'agent',
      org: 'test-org-id'
    })
  },

  // HTTP utilities
  http: {
    createAuthHeaders: (token) => ({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }),
    
    createTestRequest: (overrides = {}) => ({
      requestId: 'test-request-id',
      ip: '127.0.0.1',
      get: jest.fn().mockReturnValue('test-user-agent'),
      ...overrides
    })
  },

  // Mock utilities
  mocks: {
    createMockResponse: () => {
      const res = {};
      res.status = jest.fn().mockReturnValue(res);
      res.json = jest.fn().mockReturnValue(res);
      res.setHeader = jest.fn().mockReturnValue(res);
      res.send = jest.fn().mockReturnValue(res);
      return res;
    },
    
    createMockNext: () => jest.fn()
  },

  // Data factories
  factories: {
    user: (overrides = {}) => ({
      id: 'test-user-id',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      role: 'agent',
      organization_id: 'test-org-id',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    }),
    
    contact: (overrides = {}) => ({
      id: 'test-contact-id',
      organization_id: 'test-org-id',
      first_name: 'John',
      last_name: 'Doe',
      company: 'Test Company',
      email: '<EMAIL>',
      phone: '+**********',
      tags: ['prospect'],
      status: 'active',
      custom_fields: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    }),
    
    call: (overrides = {}) => ({
      id: 'test-call-id',
      organization_id: 'test-org-id',
      user_id: 'test-user-id',
      contact_id: 'test-contact-id',
      phone_number: '+**********',
      direction: 'outbound',
      status: 'completed',
      provider: 'twilio',
      started_at: new Date().toISOString(),
      ended_at: new Date().toISOString(),
      duration_seconds: 300,
      recording_enabled: true,
      transcription_enabled: true,
      metadata: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    })
  }
};

// Console override for cleaner test output
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('Warning: ReactDOM.render is deprecated')
  ) {
    return;
  }
  originalConsoleError.call(console, ...args);
};
