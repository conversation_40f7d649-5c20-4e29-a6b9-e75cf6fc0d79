/**
 * Health check routes tests
 */

const request = require('supertest');
const { app } = require('../server');

describe('Health Check Routes', () => {
  describe('GET /api/v1/health', () => {
    it('should return basic health status', async () => {
      const response = await request(app)
        .get('/api/v1/health')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          status: 'healthy',
          service: 'api-gateway',
          environment: 'test'
        }
      });

      expect(response.body.data.uptime).toBeGreaterThan(0);
      expect(response.body.timestamp).toBeDefined();
    });
  });

  describe('GET /api/v1/health/live', () => {
    it('should return liveness status', async () => {
      const response = await request(app)
        .get('/api/v1/health/live')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          status: 'alive'
        }
      });

      expect(response.body.timestamp).toBeDefined();
    });
  });

  describe('GET /api/v1/health/ready', () => {
    it('should return readiness status', async () => {
      const response = await request(app)
        .get('/api/v1/health/ready');

      expect(response.status).toBeOneOf([200, 503]);
      expect(response.body.success).toBeDefined();
      expect(response.body.data.status).toBeOneOf(['ready', 'not ready']);
    });
  });

  describe('GET /api/v1/health/detailed', () => {
    it('should return detailed health status', async () => {
      const response = await request(app)
        .get('/api/v1/health/detailed');

      expect(response.status).toBeOneOf([200, 503]);
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('checks');
      expect(response.body.data).toHaveProperty('details');

      // Check that all required health checks are present
      expect(response.body.data.checks).toHaveProperty('service');
      expect(response.body.data.checks).toHaveProperty('database');
      expect(response.body.data.checks).toHaveProperty('redis');
      expect(response.body.data.checks).toHaveProperty('memory');

      // Check details structure
      expect(response.body.data.details).toHaveProperty('service');
      expect(response.body.data.details).toHaveProperty('database');
      expect(response.body.data.details).toHaveProperty('redis');
      expect(response.body.data.details).toHaveProperty('memory');
      expect(response.body.data.details).toHaveProperty('system');
    });

    it('should include system information in detailed health check', async () => {
      const response = await request(app)
        .get('/api/v1/health/detailed');

      const { system } = response.body.data.details;
      
      expect(system).toHaveProperty('platform');
      expect(system).toHaveProperty('nodeVersion');
      expect(system).toHaveProperty('cpuUsage');
      expect(system.nodeVersion).toMatch(/^v\d+\.\d+\.\d+/);
    });

    it('should include memory usage information', async () => {
      const response = await request(app)
        .get('/api/v1/health/detailed');

      const { memory } = response.body.data.details;
      
      expect(memory).toHaveProperty('status');
      expect(memory).toHaveProperty('usage');
      expect(memory).toHaveProperty('limit');
      
      expect(memory.usage).toHaveProperty('rss');
      expect(memory.usage).toHaveProperty('heapTotal');
      expect(memory.usage).toHaveProperty('heapUsed');
      expect(memory.usage).toHaveProperty('external');
    });
  });
});

// Custom Jest matcher for multiple possible values
expect.extend({
  toBeOneOf(received, expected) {
    const pass = expected.includes(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be one of ${expected}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be one of ${expected}`,
        pass: false,
      };
    }
  },
});
