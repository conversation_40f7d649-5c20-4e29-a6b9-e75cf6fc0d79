/**
 * Health check routes for API Gateway
 * Provides health status for the service and its dependencies
 */

const express = require('express');
const { Pool } = require('pg');
const redis = require('redis');
const logger = require('../utils/logger');

const router = express.Router();

// Database connection pool
let dbPool;
try {
  dbPool = new Pool({
    connectionString: process.env.DATABASE_URL,
    max: 1, // Minimal pool for health checks
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  });
} catch (error) {
  logger.error('Failed to create database pool for health checks:', error);
}

// Redis client for health checks
let redisClient;
try {
  redisClient = redis.createClient({
    url: process.env.REDIS_URL,
    socket: {
      connectTimeout: 2000,
    },
  });
  
  redisClient.on('error', (err) => {
    logger.error('Redis health check client error:', err);
  });
} catch (error) {
  logger.error('Failed to create Redis client for health checks:', error);
}

/**
 * Basic health check endpoint
 * Returns 200 if the service is running
 */
router.get('/', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      status: 'healthy',
      service: 'api-gateway',
      version: process.env.npm_package_version || '1.0.0',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development'
    },
    timestamp: new Date().toISOString(),
    request_id: req.requestId
  });
});

/**
 * Detailed health check endpoint
 * Checks all dependencies and returns detailed status
 */
router.get('/detailed', async (req, res) => {
  const healthChecks = {
    service: 'healthy',
    database: 'unknown',
    redis: 'unknown',
    memory: 'healthy',
    disk: 'healthy'
  };

  const details = {
    service: {
      status: 'healthy',
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    },
    database: {
      status: 'unknown',
      responseTime: null,
      error: null
    },
    redis: {
      status: 'unknown',
      responseTime: null,
      error: null
    },
    memory: {
      status: 'healthy',
      usage: process.memoryUsage(),
      limit: null
    },
    system: {
      platform: process.platform,
      nodeVersion: process.version,
      cpuUsage: process.cpuUsage()
    }
  };

  // Check database connection
  if (dbPool) {
    try {
      const start = Date.now();
      const client = await dbPool.connect();
      await client.query('SELECT 1');
      client.release();
      const responseTime = Date.now() - start;
      
      healthChecks.database = 'healthy';
      details.database = {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
        error: null
      };
    } catch (error) {
      healthChecks.database = 'unhealthy';
      details.database = {
        status: 'unhealthy',
        responseTime: null,
        error: error.message
      };
    }
  } else {
    healthChecks.database = 'unavailable';
    details.database = {
      status: 'unavailable',
      responseTime: null,
      error: 'Database pool not initialized'
    };
  }

  // Check Redis connection
  if (redisClient) {
    try {
      if (!redisClient.isOpen) {
        await redisClient.connect();
      }
      
      const start = Date.now();
      await redisClient.ping();
      const responseTime = Date.now() - start;
      
      healthChecks.redis = 'healthy';
      details.redis = {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
        error: null
      };
    } catch (error) {
      healthChecks.redis = 'unhealthy';
      details.redis = {
        status: 'unhealthy',
        responseTime: null,
        error: error.message
      };
    }
  } else {
    healthChecks.redis = 'unavailable';
    details.redis = {
      status: 'unavailable',
      responseTime: null,
      error: 'Redis client not initialized'
    };
  }

  // Check memory usage
  const memUsage = process.memoryUsage();
  const memLimit = 1024 * 1024 * 1024; // 1GB limit for example
  
  if (memUsage.heapUsed > memLimit * 0.9) {
    healthChecks.memory = 'warning';
    details.memory.status = 'warning';
  } else if (memUsage.heapUsed > memLimit) {
    healthChecks.memory = 'unhealthy';
    details.memory.status = 'unhealthy';
  }

  details.memory.limit = `${Math.round(memLimit / 1024 / 1024)}MB`;

  // Determine overall health
  const overallHealth = Object.values(healthChecks).every(status => 
    status === 'healthy' || status === 'warning'
  ) ? 'healthy' : 'unhealthy';

  const statusCode = overallHealth === 'healthy' ? 200 : 503;

  res.status(statusCode).json({
    success: overallHealth === 'healthy',
    data: {
      status: overallHealth,
      checks: healthChecks,
      details: details,
      timestamp: new Date().toISOString()
    },
    timestamp: new Date().toISOString(),
    request_id: req.requestId
  });
});

/**
 * Readiness probe endpoint
 * Returns 200 when the service is ready to accept traffic
 */
router.get('/ready', async (req, res) => {
  try {
    // Check critical dependencies
    const checks = [];

    // Database check
    if (dbPool) {
      const client = await dbPool.connect();
      await client.query('SELECT 1');
      client.release();
      checks.push('database');
    }

    // Redis check (if configured)
    if (redisClient && process.env.REDIS_URL) {
      if (!redisClient.isOpen) {
        await redisClient.connect();
      }
      await redisClient.ping();
      checks.push('redis');
    }

    res.status(200).json({
      success: true,
      data: {
        status: 'ready',
        checks: checks,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString(),
      request_id: req.requestId
    });
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_NOT_READY',
        message: 'Service is not ready to accept traffic'
      },
      timestamp: new Date().toISOString(),
      request_id: req.requestId
    });
  }
});

/**
 * Liveness probe endpoint
 * Returns 200 if the service is alive
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      status: 'alive',
      timestamp: new Date().toISOString()
    },
    timestamp: new Date().toISOString(),
    request_id: req.requestId
  });
});

module.exports = router;
