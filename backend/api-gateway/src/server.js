/**
 * Sales Dialer API Gateway Server
 * Main entry point for the API Gateway service
 */

require('dotenv').config();
require('express-async-errors');

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');

const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');
const proxyMiddleware = require('./middleware/proxy');

// Import routes
const authRoutes = require('./routes/auth');
const healthRoutes = require('./routes/health');

const app = express();
const server = createServer(app);

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:3000",
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Organization-ID', 'X-Request-ID']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Compression and parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Request ID middleware
app.use((req, res, next) => {
  req.requestId = require('uuid').v4();
  res.setHeader('X-Request-ID', req.requestId);
  next();
});

// Health check routes (no auth required)
app.use('/api/v1/health', healthRoutes);

// Authentication routes (no auth required)
app.use('/api/v1/auth', authRoutes);

// Protected routes with authentication
app.use('/api/v1', authMiddleware);

// Proxy middleware for microservices
app.use('/api/v1/contacts', proxyMiddleware({
  target: process.env.CONTACT_SERVICE_URL || 'http://contact-service:3000',
  pathRewrite: { '^/api/v1/contacts': '/api/v1/contacts' }
}));

app.use('/api/v1/calls', proxyMiddleware({
  target: process.env.TELEPHONY_SERVICE_URL || 'http://telephony-service:3000',
  pathRewrite: { '^/api/v1/calls': '/api/v1/calls' }
}));

app.use('/api/v1/transcriptions', proxyMiddleware({
  target: process.env.TRANSCRIPTION_SERVICE_URL || 'http://transcription-service:8000',
  pathRewrite: { '^/api/v1/transcriptions': '/api/v1/transcriptions' }
}));

app.use('/api/v1/storage', proxyMiddleware({
  target: process.env.STORAGE_SERVICE_URL || 'http://storage-service:3000',
  pathRewrite: { '^/api/v1/storage': '/api/v1/storage' }
}));

// WebSocket connection handling
io.use((socket, next) => {
  // Add authentication middleware for WebSocket connections
  const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return next(new Error('Authentication error'));
  }

  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.sub;
    socket.organizationId = decoded.org;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});

io.on('connection', (socket) => {
  logger.info(`WebSocket client connected: ${socket.id}, User: ${socket.userId}`);

  // Join user-specific room
  socket.join(`user:${socket.userId}`);
  socket.join(`org:${socket.organizationId}`);

  socket.on('disconnect', () => {
    logger.info(`WebSocket client disconnected: ${socket.id}`);
  });

  // Handle call events
  socket.on('join_call', (callId) => {
    socket.join(`call:${callId}`);
    logger.info(`User ${socket.userId} joined call ${callId}`);
  });

  socket.on('leave_call', (callId) => {
    socket.leave(`call:${callId}`);
    logger.info(`User ${socket.userId} left call ${callId}`);
  });
});

// Make io available to routes
app.set('io', io);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: 'The requested resource was not found'
    },
    timestamp: new Date().toISOString(),
    request_id: req.requestId
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  logger.info(`API Gateway server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

module.exports = { app, server, io };
