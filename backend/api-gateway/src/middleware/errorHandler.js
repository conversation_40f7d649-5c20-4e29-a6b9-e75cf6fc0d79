/**
 * Global error handling middleware for API Gateway
 * Handles all errors and returns consistent error responses
 */

const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  logger.error(`Error: ${error.message}`, {
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.requestId
  });

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = { message, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = { message, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = { message, statusCode: 400 };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = { message, statusCode: 401 };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = { message, statusCode: 401 };
  }

  // PostgreSQL errors
  if (err.code === '23505') { // Unique violation
    const message = 'Duplicate entry';
    error = { message, statusCode: 400 };
  }

  if (err.code === '23503') { // Foreign key violation
    const message = 'Referenced resource not found';
    error = { message, statusCode: 400 };
  }

  if (err.code === '23502') { // Not null violation
    const message = 'Required field missing';
    error = { message, statusCode: 400 };
  }

  // Rate limiting error
  if (err.type === 'entity.too.large') {
    const message = 'Request entity too large';
    error = { message, statusCode: 413 };
  }

  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal Server Error';

  // Determine error code
  let errorCode = 'INTERNAL_SERVER_ERROR';
  
  switch (statusCode) {
    case 400:
      errorCode = 'BAD_REQUEST';
      break;
    case 401:
      errorCode = 'UNAUTHORIZED';
      break;
    case 403:
      errorCode = 'FORBIDDEN';
      break;
    case 404:
      errorCode = 'NOT_FOUND';
      break;
    case 409:
      errorCode = 'CONFLICT';
      break;
    case 413:
      errorCode = 'PAYLOAD_TOO_LARGE';
      break;
    case 422:
      errorCode = 'VALIDATION_ERROR';
      break;
    case 429:
      errorCode = 'RATE_LIMIT_EXCEEDED';
      break;
    case 500:
      errorCode = 'INTERNAL_SERVER_ERROR';
      break;
    case 502:
      errorCode = 'BAD_GATEWAY';
      break;
    case 503:
      errorCode = 'SERVICE_UNAVAILABLE';
      break;
    default:
      errorCode = 'UNKNOWN_ERROR';
  }

  const errorResponse = {
    success: false,
    error: {
      code: errorCode,
      message: message,
      ...(process.env.NODE_ENV === 'development' && { 
        stack: err.stack,
        details: error 
      })
    },
    timestamp: new Date().toISOString(),
    request_id: req.requestId
  };

  res.status(statusCode).json(errorResponse);
};

module.exports = errorHandler;
