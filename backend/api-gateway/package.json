{"name": "sales-dialer-api-gateway", "version": "1.0.0", "description": "API Gateway for Sales Dialer Application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^6.1.5", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.7.0", "express-validator": "^6.15.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "pg": "^8.10.0", "redis": "^4.6.5", "socket.io": "^4.6.1", "axios": "^1.3.4", "dotenv": "^16.0.3", "uuid": "^9.0.0", "winston": "^3.8.2", "http-proxy-middleware": "^2.0.6", "express-async-errors": "^3.1.1"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.38.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["api-gateway", "sales-dialer", "express", "nodejs"], "author": "Sales Dialer Team", "license": "MIT"}