{"name": "sales-dialer-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "axios": "^1.3.4", "socket.io-client": "^4.6.1", "@mui/material": "^5.12.0", "@mui/icons-material": "^5.11.16", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/x-data-grid": "^6.0.3", "react-hook-form": "^7.43.9", "@hookform/resolvers": "^3.0.1", "yup": "^1.0.2", "react-query": "^3.39.3", "zustand": "^4.3.7", "date-fns": "^2.29.3", "react-hot-toast": "^2.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/testing-library__jest-dom": "^5.14.5", "eslint": "^8.38.0", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1"}, "proxy": "http://localhost:3001"}