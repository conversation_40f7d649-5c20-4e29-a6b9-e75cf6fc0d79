import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>graphy,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Card,
  CardContent,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Phone,
  CallMade,
  CallReceived,
  CallEnd,
  PlayArrow,
  Pause,
  Download,
  Transcribe,
  AccessTime,
  Person,
  VolumeUp
} from '@mui/icons-material';

interface Call {
  id: number;
  contact: string;
  phone: string;
  direction: 'inbound' | 'outbound';
  status: 'completed' | 'missed' | 'ongoing';
  duration: string;
  timestamp: string;
  recording?: string;
  transcription?: string;
}

const CallsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedCall, setSelectedCall] = useState<Call | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  // Mock data
  const [calls] = useState<Call[]>([
    {
      id: 1,
      contact: '<PERSON>',
      phone: '+****************',
      direction: 'outbound',
      status: 'completed',
      duration: '5:23',
      timestamp: '2024-01-15 14:30:00',
      recording: 'call_001.mp3',
      transcription: 'Hello John, this is regarding the proposal we discussed last week...'
    },
    {
      id: 2,
      contact: 'Sarah Johnson',
      phone: '+****************',
      direction: 'inbound',
      status: 'completed',
      duration: '3:45',
      timestamp: '2024-01-15 13:15:00',
      recording: 'call_002.mp3',
      transcription: 'Hi, I wanted to follow up on the pricing information you sent...'
    },
    {
      id: 3,
      contact: 'Mike Wilson',
      phone: '+****************',
      direction: 'outbound',
      status: 'missed',
      duration: '0:00',
      timestamp: '2024-01-15 12:00:00'
    },
    {
      id: 4,
      contact: 'Emily Davis',
      phone: '+****************',
      direction: 'inbound',
      status: 'completed',
      duration: '6:12',
      timestamp: '2024-01-15 11:30:00',
      recording: 'call_004.mp3',
      transcription: 'Thank you for calling. I have some questions about the implementation timeline...'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'missed': return 'error';
      case 'ongoing': return 'warning';
      default: return 'default';
    }
  };

  const getDirectionIcon = (direction: string) => {
    return direction === 'outbound' ? <CallMade /> : <CallReceived />;
  };

  const handleCallDetails = (call: Call) => {
    setSelectedCall(call);
    setOpenDialog(true);
  };

  const handlePlayRecording = () => {
    setIsPlaying(!isPlaying);
    // TODO: Implement audio playback
  };

  const handleDownloadRecording = () => {
    // TODO: Implement download functionality
    console.log('Downloading recording for call:', selectedCall?.id);
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const filteredCalls = calls.filter(call => {
    if (tabValue === 0) return true; // All calls
    if (tabValue === 1) return call.direction === 'outbound';
    if (tabValue === 2) return call.direction === 'inbound';
    if (tabValue === 3) return call.status === 'missed';
    return true;
  });

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Call History
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View and manage your call records
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Phone />}
          color="success"
        >
          Start New Call
        </Button>
      </Box>

      {/* Call Stats */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Calls
                  </Typography>
                  <Typography variant="h4">
                    {calls.length}
                  </Typography>
                </Box>
                <Phone color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Completed
                  </Typography>
                  <Typography variant="h4">
                    {calls.filter(c => c.status === 'completed').length}
                  </Typography>
                </Box>
                <CallMade color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Missed
                  </Typography>
                  <Typography variant="h4">
                    {calls.filter(c => c.status === 'missed').length}
                  </Typography>
                </Box>
                <CallEnd color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Avg Duration
                  </Typography>
                  <Typography variant="h4">
                    4:32
                  </Typography>
                </Box>
                <AccessTime color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="All Calls" />
          <Tab label="Outbound" />
          <Tab label="Inbound" />
          <Tab label="Missed" />
        </Tabs>
      </Paper>

      {/* Calls Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Contact</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Direction</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Date & Time</TableCell>
              <TableCell>Recording</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredCalls.map((call) => (
              <TableRow key={call.id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Person />
                    {call.contact}
                  </Box>
                </TableCell>
                <TableCell>{call.phone}</TableCell>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    {getDirectionIcon(call.direction)}
                    {call.direction}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={call.status}
                    color={getStatusColor(call.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>{call.duration}</TableCell>
                <TableCell>{formatDate(call.timestamp)}</TableCell>
                <TableCell>
                  {call.recording ? (
                    <Chip
                      icon={<VolumeUp />}
                      label="Available"
                      color="primary"
                      size="small"
                    />
                  ) : (
                    <Chip
                      label="None"
                      color="default"
                      size="small"
                    />
                  )}
                </TableCell>
                <TableCell align="right">
                  <Button
                    size="small"
                    onClick={() => handleCallDetails(call)}
                  >
                    View Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Call Details Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Call Details - {selectedCall?.contact}
        </DialogTitle>
        <DialogContent>
          {selectedCall && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Call Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon><Person /></ListItemIcon>
                    <ListItemText
                      primary="Contact"
                      secondary={selectedCall.contact}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><Phone /></ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={selectedCall.phone}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><AccessTime /></ListItemIcon>
                    <ListItemText
                      primary="Duration"
                      secondary={selectedCall.duration}
                    />
                  </ListItem>
                </List>

                {selectedCall.recording && (
                  <Box mt={2}>
                    <Typography variant="h6" gutterBottom>
                      Recording
                    </Typography>
                    <Box display="flex" gap={1}>
                      <Button
                        startIcon={isPlaying ? <Pause /> : <PlayArrow />}
                        onClick={handlePlayRecording}
                        variant="outlined"
                      >
                        {isPlaying ? 'Pause' : 'Play'}
                      </Button>
                      <Button
                        startIcon={<Download />}
                        onClick={handleDownloadRecording}
                        variant="outlined"
                      >
                        Download
                      </Button>
                    </Box>
                  </Box>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
                {selectedCall.transcription && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Transcription
                    </Typography>
                    <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                      <Typography variant="body2">
                        {selectedCall.transcription}
                      </Typography>
                    </Paper>
                  </Box>
                )}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CallsPage;
