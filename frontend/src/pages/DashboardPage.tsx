import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Phone,
  Contacts,
  CallMade,
  CallReceived,
  AccessTime,
  TrendingUp,
  RecordVoiceOver,
  Storage
} from '@mui/icons-material';

const DashboardPage: React.FC = () => {
  // Mock data for demonstration
  const stats = {
    totalCalls: 156,
    callsToday: 23,
    avgCallDuration: '4:32',
    transcriptionAccuracy: 94,
    totalContacts: 1247,
    activeContacts: 89
  };

  const recentCalls = [
    { id: 1, contact: '<PERSON>', duration: '5:23', status: 'completed', time: '2 hours ago' },
    { id: 2, contact: '<PERSON>', duration: '3:45', status: 'completed', time: '3 hours ago' },
    { id: 3, contact: '<PERSON>', duration: '2:15', status: 'missed', time: '4 hours ago' },
    { id: 4, contact: '<PERSON>', duration: '6:12', status: 'completed', time: '5 hours ago' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'missed': return 'error';
      case 'ongoing': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box p={3}>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Welcome back! Here's your call activity overview.
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Calls
                  </Typography>
                  <Typography variant="h4">
                    {stats.totalCalls}
                  </Typography>
                </Box>
                <Phone color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Calls Today
                  </Typography>
                  <Typography variant="h4">
                    {stats.callsToday}
                  </Typography>
                </Box>
                <CallMade color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Avg Duration
                  </Typography>
                  <Typography variant="h4">
                    {stats.avgCallDuration}
                  </Typography>
                </Box>
                <AccessTime color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Transcription
                  </Typography>
                  <Typography variant="h4">
                    {stats.transcriptionAccuracy}%
                  </Typography>
                </Box>
                <RecordVoiceOver color="secondary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Calls */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Recent Calls
              </Typography>
              <Button variant="outlined" size="small">
                View All
              </Button>
            </Box>
            <List>
              {recentCalls.map((call) => (
                <ListItem key={call.id} divider>
                  <ListItemIcon>
                    {call.status === 'completed' ? (
                      <CallMade color="success" />
                    ) : (
                      <CallReceived color="error" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={call.contact}
                    secondary={`Duration: ${call.duration} • ${call.time}`}
                  />
                  <Chip
                    label={call.status}
                    color={getStatusColor(call.status) as any}
                    size="small"
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Quick Actions & Stats */}
        <Grid item xs={12} md={4}>
          <Grid container spacing={2}>
            {/* Quick Actions */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Quick Actions
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="contained"
                    startIcon={<Phone />}
                    fullWidth
                  >
                    Start New Call
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Contacts />}
                    fullWidth
                  >
                    Add Contact
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<TrendingUp />}
                    fullWidth
                  >
                    View Analytics
                  </Button>
                </Box>
              </Paper>
            </Grid>

            {/* System Status */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  System Status
                </Typography>
                <Box mb={2}>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">
                      Storage Used
                    </Typography>
                    <Typography variant="body2">
                      68%
                    </Typography>
                  </Box>
                  <LinearProgress variant="determinate" value={68} />
                </Box>
                <Box mb={2}>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">
                      API Health
                    </Typography>
                    <Chip label="Healthy" color="success" size="small" />
                  </Box>
                </Box>
                <Box>
                  <Box display="flex" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">
                      Transcription Service
                    </Typography>
                    <Chip label="Online" color="success" size="small" />
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
