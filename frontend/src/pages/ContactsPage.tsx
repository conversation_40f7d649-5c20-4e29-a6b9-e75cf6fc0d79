import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  InputAdornment,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Avatar,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid
} from '@mui/material';
import {
  Search,
  Add,
  Phone,
  Email,
  MoreVert,
  Edit,
  Delete,
  Person
} from '@mui/icons-material';

interface Contact {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  tags: string[];
  lastCall?: string;
}

const ContactsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [openDialog, setOpenDialog] = useState(false);

  // Mock data
  const [contacts] = useState<Contact[]>([
    {
      id: 1,
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'Acme Corp',
      tags: ['Lead', 'Hot'],
      lastCall: '2 days ago'
    },
    {
      id: 2,
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'TechStart Inc',
      tags: ['Customer', 'VIP'],
      lastCall: '1 week ago'
    },
    {
      id: 3,
      firstName: 'Mike',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'Freelancer',
      tags: ['Prospect'],
      lastCall: 'Never'
    },
    {
      id: 4,
      firstName: 'Emily',
      lastName: 'Davis',
      email: '<EMAIL>',
      phone: '+****************',
      company: 'BigCorp Ltd',
      tags: ['Lead', 'Warm'],
      lastCall: '3 days ago'
    }
  ]);

  const filteredContacts = contacts.filter(contact =>
    `${contact.firstName} ${contact.lastName} ${contact.email} ${contact.company}`
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
  );

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, contact: Contact) => {
    setAnchorEl(event.currentTarget);
    setSelectedContact(contact);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedContact(null);
  };

  const handleCall = (contact: Contact) => {
    console.log('Calling:', contact);
    handleMenuClose();
    // TODO: Implement call functionality
  };

  const handleEdit = (contact: Contact) => {
    console.log('Editing:', contact);
    setOpenDialog(true);
    handleMenuClose();
  };

  const handleDelete = (contact: Contact) => {
    console.log('Deleting:', contact);
    handleMenuClose();
    // TODO: Implement delete functionality
  };

  const getTagColor = (tag: string) => {
    switch (tag.toLowerCase()) {
      case 'hot': return 'error';
      case 'warm': return 'warning';
      case 'cold': return 'info';
      case 'customer': return 'success';
      case 'vip': return 'secondary';
      default: return 'default';
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Contacts
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your contact database
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpenDialog(true)}
        >
          Add Contact
        </Button>
      </Box>

      {/* Search */}
      <Box mb={3}>
        <TextField
          fullWidth
          placeholder="Search contacts..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          sx={{ maxWidth: 400 }}
        />
      </Box>

      {/* Contacts Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Contact</TableCell>
              <TableCell>Company</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Tags</TableCell>
              <TableCell>Last Call</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredContacts.map((contact) => (
              <TableRow key={contact.id} hover>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {getInitials(contact.firstName, contact.lastName)}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {contact.firstName} {contact.lastName}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>{contact.company}</TableCell>
                <TableCell>{contact.phone}</TableCell>
                <TableCell>{contact.email}</TableCell>
                <TableCell>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    {contact.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        size="small"
                        color={getTagColor(tag) as any}
                      />
                    ))}
                  </Box>
                </TableCell>
                <TableCell>{contact.lastCall}</TableCell>
                <TableCell align="right">
                  <IconButton
                    onClick={(e) => handleMenuClick(e, contact)}
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedContact && handleCall(selectedContact)}>
          <Phone sx={{ mr: 1 }} />
          Call
        </MenuItem>
        <MenuItem onClick={() => selectedContact && handleEdit(selectedContact)}>
          <Edit sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => selectedContact && handleDelete(selectedContact)}>
          <Delete sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Add/Edit Contact Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedContact ? 'Edit Contact' : 'Add New Contact'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="First Name"
                defaultValue={selectedContact?.firstName || ''}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Last Name"
                defaultValue={selectedContact?.lastName || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                defaultValue={selectedContact?.email || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Phone"
                defaultValue={selectedContact?.phone || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Company"
                defaultValue={selectedContact?.company || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Tags (comma separated)"
                defaultValue={selectedContact?.tags.join(', ') || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={() => setOpenDialog(false)}>
            {selectedContact ? 'Update' : 'Add'} Contact
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContactsPage;
