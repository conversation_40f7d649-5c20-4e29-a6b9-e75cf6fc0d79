import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Grid,
  Card,
  CardContent,
  Divider,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip
} from '@mui/material';
import {
  Person,
  Security,
  Phone,
  Storage,
  Notifications,
  Save
} from '@mui/icons-material';

const SettingsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [settings, setSettings] = useState({
    // Profile settings
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    
    // Call settings
    autoRecord: true,
    transcriptionEnabled: true,
    transcriptionLanguage: 'en',
    callTimeout: 30,
    
    // Notification settings
    emailNotifications: true,
    smsNotifications: false,
    callAlerts: true,
    
    // Security settings
    twoFactorEnabled: false,
    sessionTimeout: 60,
    
    // Storage settings
    storageProvider: 'minio',
    autoBackup: true,
    retentionDays: 90
  });

  const [saved, setSaved] = useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Saving settings:', settings);
    setSaved(true);
    setTimeout(() => setSaved(false), 3000);
  };

  const renderProfileTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Personal Information
        </Typography>
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="First Name"
          value={settings.firstName}
          onChange={(e) => handleSettingChange('firstName', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Last Name"
          value={settings.lastName}
          onChange={(e) => handleSettingChange('lastName', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Email"
          type="email"
          value={settings.email}
          onChange={(e) => handleSettingChange('email', e.target.value)}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Phone"
          value={settings.phone}
          onChange={(e) => handleSettingChange('phone', e.target.value)}
        />
      </Grid>
    </Grid>
  );

  const renderCallTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Call Settings
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.autoRecord}
              onChange={(e) => handleSettingChange('autoRecord', e.target.checked)}
            />
          }
          label="Automatically record calls"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.transcriptionEnabled}
              onChange={(e) => handleSettingChange('transcriptionEnabled', e.target.checked)}
            />
          }
          label="Enable real-time transcription"
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Transcription Language</InputLabel>
          <Select
            value={settings.transcriptionLanguage}
            onChange={(e) => handleSettingChange('transcriptionLanguage', e.target.value)}
            label="Transcription Language"
          >
            <MenuItem value="en">English</MenuItem>
            <MenuItem value="es">Spanish</MenuItem>
            <MenuItem value="fr">French</MenuItem>
            <MenuItem value="de">German</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Call Timeout (seconds)"
          type="number"
          value={settings.callTimeout}
          onChange={(e) => handleSettingChange('callTimeout', parseInt(e.target.value))}
        />
      </Grid>
    </Grid>
  );

  const renderNotificationTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Notification Preferences
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.emailNotifications}
              onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
            />
          }
          label="Email notifications"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.smsNotifications}
              onChange={(e) => handleSettingChange('smsNotifications', e.target.checked)}
            />
          }
          label="SMS notifications"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.callAlerts}
              onChange={(e) => handleSettingChange('callAlerts', e.target.checked)}
            />
          }
          label="Call alerts"
        />
      </Grid>
    </Grid>
  );

  const renderSecurityTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Security Settings
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.twoFactorEnabled}
              onChange={(e) => handleSettingChange('twoFactorEnabled', e.target.checked)}
            />
          }
          label="Enable two-factor authentication"
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Session Timeout (minutes)"
          type="number"
          value={settings.sessionTimeout}
          onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12}>
        <Button variant="outlined" color="warning">
          Change Password
        </Button>
      </Grid>
    </Grid>
  );

  const renderStorageTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Storage & Backup
        </Typography>
      </Grid>
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <InputLabel>Storage Provider</InputLabel>
          <Select
            value={settings.storageProvider}
            onChange={(e) => handleSettingChange('storageProvider', e.target.value)}
            label="Storage Provider"
          >
            <MenuItem value="minio">MinIO (Local)</MenuItem>
            <MenuItem value="s3">Amazon S3</MenuItem>
            <MenuItem value="gcs">Google Cloud Storage</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          label="Retention Period (days)"
          type="number"
          value={settings.retentionDays}
          onChange={(e) => handleSettingChange('retentionDays', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.autoBackup}
              onChange={(e) => handleSettingChange('autoBackup', e.target.checked)}
            />
          }
          label="Enable automatic backup"
        />
      </Grid>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              Storage Usage
            </Typography>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">Recordings</Typography>
              <Typography variant="body2">2.3 GB</Typography>
            </Box>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">Transcriptions</Typography>
              <Typography variant="body2">45 MB</Typography>
            </Box>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2">Total Used</Typography>
              <Typography variant="body2">2.35 GB</Typography>
            </Box>
            <Divider sx={{ my: 1 }} />
            <Box display="flex" justifyContent="space-between">
              <Typography variant="body2" fontWeight="bold">
                Available
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                7.65 GB
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const tabs = [
    { label: 'Profile', icon: <Person />, content: renderProfileTab },
    { label: 'Calls', icon: <Phone />, content: renderCallTab },
    { label: 'Notifications', icon: <Notifications />, content: renderNotificationTab },
    { label: 'Security', icon: <Security />, content: renderSecurityTab },
    { label: 'Storage', icon: <Storage />, content: renderStorageTab }
  ];

  return (
    <Box p={3}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom>
          Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your application preferences and configuration
        </Typography>
      </Box>

      {/* Success Alert */}
      {saved && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Settings saved successfully!
        </Alert>
      )}

      {/* Settings Tabs */}
      <Paper>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>

        <Box p={3}>
          {tabs[tabValue].content()}
        </Box>

        <Divider />

        <Box p={3} display="flex" justifyContent="flex-end">
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSave}
          >
            Save Settings
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default SettingsPage;
