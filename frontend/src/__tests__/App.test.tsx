import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import App from '../App';

// Mock the auth hook
jest.mock('../hooks/useAuth', () => ({
  useAuth: () => ({
    user: null,
    loading: false,
    error: null
  })
}));

// Mock the pages
jest.mock('../pages/LoginPage', () => {
  return function LoginPage() {
    return <div data-testid="login-page">Login Page</div>;
  };
});

jest.mock('../pages/DashboardPage', () => {
  return function DashboardPage() {
    return <div data-testid="dashboard-page">Dashboard Page</div>;
  };
});

jest.mock('../pages/ContactsPage', () => {
  return function ContactsPage() {
    return <div data-testid="contacts-page">Contacts Page</div>;
  };
});

jest.mock('../pages/CallsPage', () => {
  return function CallsPage() {
    return <div data-testid="calls-page">Calls Page</div>;
  };
});

jest.mock('../pages/SettingsPage', () => {
  return function SettingsPage() {
    return <div data-testid="settings-page">Settings Page</div>;
  };
});

// Mock the components
jest.mock('../components/Layout', () => {
  return function Layout({ children }: { children: React.ReactNode }) {
    return <div data-testid="layout">{children}</div>;
  };
});

jest.mock('../components/ProtectedRoute', () => {
  return function ProtectedRoute({ children }: { children: React.ReactNode }) {
    return <div data-testid="protected-route">{children}</div>;
  };
});

jest.mock('../components/LoadingSpinner', () => {
  return function LoadingSpinner() {
    return <div data-testid="loading-spinner">Loading...</div>;
  };
});

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const theme = createTheme();

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  
  return render(
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <BrowserRouter>
          {component}
        </BrowserRouter>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

describe('App Component', () => {
  beforeEach(() => {
    // Reset window location
    window.history.pushState({}, '', '/');
  });

  it('renders without crashing', () => {
    renderWithProviders(<App />);
  });

  it('shows login page when user is not authenticated', () => {
    // Navigate to login page
    window.history.pushState({}, '', '/login');
    
    renderWithProviders(<App />);
    
    expect(screen.getByTestId('login-page')).toBeInTheDocument();
  });

  it('redirects to login when accessing protected route without authentication', () => {
    // Try to access dashboard without authentication
    window.history.pushState({}, '', '/dashboard');
    
    renderWithProviders(<App />);
    
    // Should redirect to login
    expect(window.location.pathname).toBe('/dashboard');
  });
});

describe('App Component with Authentication', () => {
  beforeEach(() => {
    // Mock authenticated user
    jest.doMock('../hooks/useAuth', () => ({
      useAuth: () => ({
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: 'agent'
        },
        loading: false,
        error: null
      })
    }));
  });

  afterEach(() => {
    jest.dontMock('../hooks/useAuth');
  });

  it('shows dashboard when user is authenticated and accessing root', () => {
    window.history.pushState({}, '', '/');
    
    renderWithProviders(<App />);
    
    expect(screen.getByTestId('protected-route')).toBeInTheDocument();
    expect(screen.getByTestId('layout')).toBeInTheDocument();
  });

  it('redirects to dashboard when authenticated user tries to access login', () => {
    window.history.pushState({}, '', '/login');
    
    renderWithProviders(<App />);
    
    // Should redirect away from login page
    expect(screen.queryByTestId('login-page')).not.toBeInTheDocument();
  });
});

describe('App Component Loading State', () => {
  beforeEach(() => {
    // Mock loading state
    jest.doMock('../hooks/useAuth', () => ({
      useAuth: () => ({
        user: null,
        loading: true,
        error: null
      })
    }));
  });

  afterEach(() => {
    jest.dontMock('../hooks/useAuth');
  });

  it('shows loading spinner when authentication is loading', () => {
    renderWithProviders(<App />);
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });
});
