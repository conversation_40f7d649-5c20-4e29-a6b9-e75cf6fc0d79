// Type definitions for Sales Dialer Application

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'manager' | 'agent' | 'viewer';
  organization_id: string;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  id: string;
  name: string;
  domain?: string;
  settings: Record<string, any>;
  subscription_plan: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Contact {
  id: string;
  organization_id: string;
  external_id?: string;
  first_name: string;
  last_name: string;
  company?: string;
  title?: string;
  email?: string;
  phone?: string;
  mobile_phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  custom_fields: Record<string, any>;
  tags: string[];
  status: 'active' | 'inactive' | 'do_not_call';
  last_contacted_at?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface Call {
  id: string;
  organization_id: string;
  user_id: string;
  contact_id?: string;
  phone_number: string;
  direction: 'inbound' | 'outbound';
  status: 'initiated' | 'ringing' | 'answered' | 'completed' | 'failed' | 'cancelled';
  provider: string;
  provider_call_id?: string;
  started_at: string;
  answered_at?: string;
  ended_at?: string;
  duration_seconds?: number;
  recording_enabled: boolean;
  transcription_enabled: boolean;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CallRecording {
  id: string;
  call_id: string;
  filename: string;
  format: 'opus' | 'mp3' | 'wav' | 'flac';
  duration_seconds?: number;
  file_size_bytes?: number;
  checksum?: string;
  storage_path?: string;
  storage_backend: string;
  is_encrypted: boolean;
  encryption_key_id?: string;
  created_at: string;
}

export interface CallTranscription {
  id: string;
  call_id: string;
  content: string;
  language: string;
  confidence_score?: number;
  processing_time_ms?: number;
  model_version?: string;
  speaker_segments?: SpeakerSegment[];
  keywords?: string[];
  sentiment_analysis?: SentimentAnalysis;
  created_at: string;
  updated_at: string;
}

export interface SpeakerSegment {
  speaker: string;
  start_time: number;
  end_time: number;
  text: string;
}

export interface SentimentAnalysis {
  overall_sentiment: 'positive' | 'negative' | 'neutral';
  sentiment_score: number;
  emotions?: string[];
}

export interface ContactGroup {
  id: string;
  organization_id: string;
  name: string;
  description?: string;
  criteria?: Record<string, any>;
  is_dynamic: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  request_id: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: User;
}

export interface CallInitiateRequest {
  contact_id?: string;
  phone_number: string;
  recording_enabled?: boolean;
  transcription_enabled?: boolean;
}

export interface ContactCreateRequest {
  first_name: string;
  last_name: string;
  company?: string;
  title?: string;
  email?: string;
  phone?: string;
  mobile_phone?: string;
  address?: Contact['address'];
  custom_fields?: Record<string, any>;
  tags?: string[];
}

export interface ContactUpdateRequest extends Partial<ContactCreateRequest> {
  id: string;
}

export interface ContactFilters {
  search?: string;
  tags?: string[];
  status?: Contact['status'];
  company?: string;
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface CallFilters {
  status?: Call['status'];
  direction?: Call['direction'];
  contact_id?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// WebSocket event types
export interface WebSocketEvent {
  type: string;
  data: any;
  timestamp: string;
}

export interface CallStatusEvent extends WebSocketEvent {
  type: 'call_status';
  data: {
    call_id: string;
    status: Call['status'];
    timestamp: string;
  };
}

export interface TranscriptionChunkEvent extends WebSocketEvent {
  type: 'transcription_chunk';
  data: {
    call_id: string;
    speaker: string;
    text: string;
    confidence: number;
    is_final: boolean;
    timestamp: string;
  };
}

export interface AudioQualityEvent extends WebSocketEvent {
  type: 'audio_quality';
  data: {
    call_id: string;
    quality_score: number;
    latency_ms: number;
    packet_loss: number;
    timestamp: string;
  };
}

// Store types
export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
}

export interface CallState {
  currentCall: Call | null;
  calls: Call[];
  loading: boolean;
  error: string | null;
}

export interface ContactState {
  contacts: Contact[];
  selectedContact: Contact | null;
  loading: boolean;
  error: string | null;
  filters: ContactFilters;
}
