# Sales Dialer Application - Environment Configuration
# Copy this file to .env and update with your actual values

# Application Environment
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/salesdialer
POSTGRES_DB=salesdialer
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# Encryption Configuration
BCRYPT_ROUNDS=12
ENCRYPTION_KEY=your-32-character-encryption-key

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# VoIP.ms Configuration (Alternative to Twilio)
VOIPMS_API_USERNAME=your_voipms_username
VOIPMS_API_PASSWORD=your_voipms_password

# Google Drive Configuration
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id
GOOGLE_DRIVE_CLIENT_ID=your_google_drive_client_id
GOOGLE_DRIVE_CLIENT_SECRET=your_google_drive_client_secret
GOOGLE_DRIVE_REFRESH_TOKEN=your_google_drive_refresh_token

# MinIO/S3 Configuration
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_USE_SSL=false
MINIO_BUCKET_NAME=sales-dialer-recordings

# CRM Integration
CRM_API_URL=your_crm_api_url
CRM_API_KEY=your_crm_api_key

# Transcription Service Configuration
WHISPER_MODEL=base
WHISPER_LANGUAGE=en
GPU_ENABLED=false
MAX_WORKERS=2

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123
WEBHOOK_URL=http://localhost:5678

# Monitoring and Logging
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true

# Security Configuration
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Storage Configuration
UPLOAD_MAX_SIZE=100MB
TEMP_DIR=./temp
CALLS_DIR=./calls

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
FROM_EMAIL=<EMAIL>

# Development Configuration
CHOKIDAR_USEPOLLING=true
REACT_APP_API_URL=http://localhost:3001
REACT_APP_WS_URL=ws://localhost:3001
