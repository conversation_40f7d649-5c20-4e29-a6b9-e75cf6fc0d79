#!/bin/bash

# Script de reparación rápida para dependencias de DaVinci Resolve
# Para Zorin OS 17.3 / Ubuntu 22.04

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "=== Reparación de dependencias para DaVinci Resolve ==="

# Instalar dependencias básicas que están disponibles
print_status "Instalando dependencias disponibles..."
sudo apt install -y \
    wget \
    curl \
    unzip \
    libapr1 \
    libaprutil1 \
    libxcb-composite0 \
    libxcb-cursor0 \
    libxcb-damage0 \
    libxcb-xinerama0 \
    libxcb-xinput0 \
    libxcb-xkb1 \
    libxkbcommon-x11-0 \
    libxss1 \
    libxrandr2 \
    libasound2 \
    libatk1.0-0 \
    libdrm-amdgpu1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxfixes3 \
    libxi6 \
    libxrender1 \
    libxtst6 \
    libxxf86vm1 \
    libegl1-mesa \
    libgl1-mesa-glx \
    libgles2-mesa \
    mesa-vulkan-drivers

print_success "Dependencias básicas instaladas"

# Instalar libssl1.1 manualmente
print_status "Instalando libssl1.1 manualmente..."
cd /tmp
wget -q http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2.23_amd64.deb
sudo dpkg -i libssl1.1_1.1.1f-1ubuntu2.23_amd64.deb
sudo apt-get install -f -y
rm -f libssl1.1_1.1.1f-1ubuntu2.23_amd64.deb

print_success "libssl1.1 instalado correctamente"

# Intentar instalar libgconf-2-4 si está disponible
print_status "Intentando instalar libgconf-2-4..."
if sudo apt install -y libgconf-2-4 2>/dev/null; then
    print_success "libgconf-2-4 instalado"
else
    print_warning "libgconf-2-4 no disponible, pero DaVinci Resolve debería funcionar sin él"
fi

print_success "¡Dependencias reparadas! Ahora puedes continuar con la instalación de DaVinci Resolve"
print_status "Ejecuta: ./install_davinci_resolve.sh"
