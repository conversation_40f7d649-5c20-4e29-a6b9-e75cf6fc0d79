#!/bin/bash

# Script para instalar DaVinci Resolve en Zorin OS 17.3
# Autor: Asistente AI
# Fecha: $(date)
#
# IMPORTANTE: Este script debe ejecutarse en el terminal del sistema de Zorin OS
# No funcionará en entornos con restricciones de privilegios
#
# Uso: ./install_davinci_resolve.sh

set -e  # Salir si hay algún error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir mensajes con colores
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar si el script se ejecuta como root para ciertas operaciones
check_sudo() {
    if [ "$EUID" -eq 0 ]; then
        print_warning "Ejecutándose como root"
    else
        print_status "Ejecutándose como usuario normal"
    fi

    # Verificar si sudo está disponible y funcional
    if ! sudo -n true 2>/dev/null; then
        print_error "Este script requiere privilegios sudo para instalar paquetes"
        print_error "Por favor ejecuta este script en el terminal del sistema de Zorin OS"
        print_error "No en un entorno con restricciones de privilegios"
        print_status "Instrucciones:"
        print_status "1. Abre el terminal del sistema (Ctrl+Alt+T)"
        print_status "2. Navega al directorio: cd '$(pwd)'"
        print_status "3. Ejecuta: ./install_davinci_resolve.sh"
        exit 1
    fi
}

# Función principal
main() {
    print_status "=== Instalador de DaVinci Resolve para Zorin OS ==="
    
    check_sudo
    
    # Paso 1: Verificar sistema
    print_status "Verificando sistema operativo..."
    if ! grep -q "Zorin" /etc/os-release; then
        print_warning "Este script está diseñado para Zorin OS, pero continuaremos..."
    fi
    
    # Paso 2: Actualizar sistema
    print_status "Actualizando sistema..."
    sudo apt update
    sudo apt upgrade -y
    
    # Paso 3: Instalar dependencias
    print_status "Instalando dependencias necesarias..."

    # Primero instalar dependencias básicas
    sudo apt install -y \
        wget \
        curl \
        unzip \
        libapr1 \
        libaprutil1 \
        libxcb-composite0 \
        libxcb-cursor0 \
        libxcb-damage0 \
        libxcb-xinerama0 \
        libxcb-xinput0 \
        libxcb-xkb1 \
        libxkbcommon-x11-0 \
        libxss1 \
        libxrandr2 \
        libasound2 \
        libatk1.0-0 \
        libdrm-amdgpu1 \
        libxcomposite1 \
        libxcursor1 \
        libxdamage1 \
        libxfixes3 \
        libxi6 \
        libxrender1 \
        libxtst6 \
        libxxf86vm1 \
        libegl1-mesa \
        libgl1-mesa-glx \
        libgles2-mesa \
        mesa-vulkan-drivers

    # Intentar instalar libssl1.1 desde repositorio alternativo si no está disponible
    if ! apt list --installed libssl1.1 2>/dev/null | grep -q libssl1.1; then
        print_status "libssl1.1 no disponible, intentando instalar desde repositorio alternativo..."

        # Descargar e instalar libssl1.1 manualmente para Ubuntu 22.04
        cd /tmp
        wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2.23_amd64.deb
        sudo dpkg -i libssl1.1_1.1.1f-1ubuntu2.23_amd64.deb || true
        sudo apt-get install -f -y
        rm -f libssl1.1_1.1.1f-1ubuntu2.23_amd64.deb
    fi

    # Intentar instalar libgconf-2-4 si está disponible
    sudo apt install -y libgconf-2-4 || print_warning "libgconf-2-4 no disponible, continuando..."
    
    print_success "Dependencias instaladas correctamente"
    
    # Paso 4: Verificar si ya existe DaVinci Resolve
    if [ -d "/opt/resolve" ]; then
        print_warning "DaVinci Resolve ya parece estar instalado en /opt/resolve"
        read -p "¿Deseas continuar con la instalación? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Instalación cancelada por el usuario"
            exit 0
        fi
    fi
    
    # Paso 5: Crear directorio temporal
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    print_status "Directorio temporal creado: $TEMP_DIR"
    
    # Paso 6: Buscar archivo de DaVinci Resolve en Downloads
    DOWNLOADS_DIR="$HOME/Downloads"
    RESOLVE_FILE=$(find "$DOWNLOADS_DIR" -name "DaVinci_Resolve_*_Linux.zip" -o -name "DaVinci_Resolve_*_Linux.run" | head -1)
    
    if [ -z "$RESOLVE_FILE" ]; then
        print_error "No se encontró archivo de DaVinci Resolve en $DOWNLOADS_DIR"
        print_status "Por favor:"
        print_status "1. Ve a https://www.blackmagicdesign.com/products/davinciresolve"
        print_status "2. Descarga DaVinci Resolve para Linux"
        print_status "3. Guarda el archivo en $DOWNLOADS_DIR"
        print_status "4. Ejecuta este script nuevamente"
        exit 1
    fi
    
    print_success "Archivo encontrado: $RESOLVE_FILE"
    
    # Paso 7: Procesar archivo según su tipo
    if [[ "$RESOLVE_FILE" == *.zip ]]; then
        print_status "Extrayendo archivo ZIP..."
        unzip "$RESOLVE_FILE" -d .
        INSTALLER=$(find . -name "*.run" | head -1)
    else
        print_status "Copiando instalador..."
        cp "$RESOLVE_FILE" .
        INSTALLER=$(basename "$RESOLVE_FILE")
    fi
    
    if [ -z "$INSTALLER" ]; then
        print_error "No se encontró el instalador .run"
        exit 1
    fi
    
    # Paso 8: Hacer ejecutable e instalar
    print_status "Haciendo ejecutable el instalador..."
    chmod +x "$INSTALLER"
    
    print_status "Iniciando instalación de DaVinci Resolve..."
    print_warning "Se abrirá una ventana de instalación. Sigue las instrucciones en pantalla."
    
    sudo ./"$INSTALLER"
    
    # Paso 9: Verificar instalación
    if [ -d "/opt/resolve" ]; then
        print_success "¡DaVinci Resolve instalado correctamente!"
        
        # Crear enlace simbólico para facilitar ejecución
        if [ ! -L "/usr/local/bin/resolve" ]; then
            sudo ln -s /opt/resolve/bin/resolve /usr/local/bin/resolve
            print_success "Enlace simbólico creado en /usr/local/bin/resolve"
        fi
        
        print_status "Puedes ejecutar DaVinci Resolve con:"
        print_status "- Desde el menú de aplicaciones"
        print_status "- Desde terminal: resolve"
        print_status "- Desde terminal: /opt/resolve/bin/resolve"
        
    else
        print_error "La instalación no se completó correctamente"
        exit 1
    fi
    
    # Paso 10: Limpiar archivos temporales
    cd /
    rm -rf "$TEMP_DIR"
    print_success "Archivos temporales eliminados"
    
    # Paso 11: Información adicional
    print_status "=== Información adicional ==="
    print_status "Para un mejor rendimiento, asegúrate de tener:"
    print_status "- GPU dedicada (NVIDIA/AMD)"
    print_status "- Al menos 16GB de RAM"
    print_status "- Drivers de GPU actualizados"
    
    if lspci | grep -i nvidia > /dev/null; then
        print_status "GPU NVIDIA detectada. Considera instalar drivers propietarios:"
        print_status "sudo apt install nvidia-driver-470 nvidia-settings"
    fi
    
    print_success "¡Instalación completada!"
}

# Ejecutar función principal
main "$@"
