# Sales Dialer AI Transcriber

A comprehensive sales dialer application with real-time AI transcription, call recording, and multi-backend storage capabilities. Built for sales representatives to make calls, record them, transcribe in real-time, and store information across multiple platforms.

## 🚀 Features

- **Contact Management**: Complete contact book with company and phone number display
- **Telephony Integration**: Twilio and VoIP.ms support for making calls
- **Real-time Transcription**: Live speech-to-text using Whisper AI or Mozilla DeepSpeech
- **Call Recording**: High-quality Opus format recording with automatic storage
- **Multi-Backend Storage**: Google Drive, MinIO S3, and CRM integration
- **Workflow Automation**: n8n integration for automated processing
- **Analytics & Reporting**: Call metrics, sentiment analysis, and performance insights
- **Security & Compliance**: GDPR/CCPA compliant with encryption and audit logging

## 📋 Requirements

- Docker and Docker Compose
- Node.js 18+ (for development)
- Python 3.11+ (for transcription service)
- Twilio account (or VoIP.ms)
- Google Drive API credentials (optional)
- MinIO or S3-compatible storage

## 🏗️ Architecture

The application supports two deployment strategies:

### Monolithic Deployment (Original Specification)
- Single container with all services
- Perfect for development and small deployments
- Matches original specification exactly

### Microservices Deployment (Scalable)
- Separate containers for each service
- Horizontal scaling capabilities
- Production-ready architecture

## 📚 Documentation

Comprehensive planning and implementation documentation is available in the `/docs` directory:

- **[00-Consistency-Review-Summary.md](docs/00-Consistency-Review-Summary.md)** - Review of all planning documents
- **[01-Feasibility-Assessment.md](docs/01-Feasibility-Assessment.md)** - Technical feasibility and risk analysis
- **[02-System-Architecture-Design.md](docs/02-System-Architecture-Design.md)** - Complete system architecture
- **[03-Database-Schema-Data-Management.md](docs/03-Database-Schema-Data-Management.md)** - Database design and data management
- **[04-API-Design-Specifications.md](docs/04-API-Design-Specifications.md)** - RESTful API endpoints and specifications
- **[05-Security-Implementation-Plan.md](docs/05-Security-Implementation-Plan.md)** - Security framework and compliance
- **[06-Docker-Containerization-Strategy.md](docs/06-Docker-Containerization-Strategy.md)** - Container deployment strategies
- **[07-Development-Phases-Milestones.md](docs/07-Development-Phases-Milestones.md)** - 16-week development roadmap
- **[08-Testing-Deployment-Procedures.md](docs/08-Testing-Deployment-Procedures.md)** - Testing and deployment procedures

## 🚀 Quick Start

### Monolithic Deployment (Recommended for Development)

1. Clone the repository:
```bash
git clone https://github.com/c42705/sales-dialer-AI-transcript.git
cd sales-dialer-AI-transcript
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start the application:
```bash
docker-compose -f docker-compose.monolith.yml up -d
```

4. Access the application:
- Web Interface: http://localhost:3000
- MinIO Console: http://localhost:9000
- n8n Workflows: http://localhost:5678

### Microservices Deployment (Production)

1. Use the development configuration:
```bash
docker-compose -f docker-compose.dev.yml up -d
```

2. Or production configuration:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🛠️ Development

### Prerequisites
- Node.js 18+
- Python 3.11+
- Docker and Docker Compose
- Git

### Development Setup
1. Follow the [Development Phases](docs/07-Development-Phases-Milestones.md) document
2. Start with Phase 1: Foundation and Core Infrastructure
3. Use the monolithic deployment for initial development
4. Migrate to microservices as features are added

### Testing
```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e
```

## 📁 File Structure

The application maintains compatibility with the original file structure:

```
/calls
  /2025-06-11
    /call_001
      - recording.opus
      - transcription.txt
      - metadata.json
    /call_002
      - recording.opus
      - transcription.txt
      - metadata.json
```

## 🔧 Configuration

### Environment Variables

Key environment variables for configuration:

```bash
# Telephony
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token

# Storage
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id
MINIO_ENDPOINT=your_minio_endpoint
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key

# CRM Integration
CRM_API_URL=your_crm_api_url

# Database
DATABASE_URL=postgresql://user:password@host:port/database
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- Documentation: Check the `/docs` directory for comprehensive guides
- Issues: Open an issue on GitHub for bug reports or feature requests
- Development: Follow the 16-week development plan in the documentation

## 🗺️ Roadmap

- **Phase 1 (Weeks 1-4)**: Foundation and Core Infrastructure
- **Phase 2 (Weeks 5-8)**: Telephony Integration and Call Management
- **Phase 3 (Weeks 9-12)**: AI Transcription and Analytics
- **Phase 4 (Weeks 13-16)**: Integration and Production Readiness

See [Development Phases](docs/07-Development-Phases-Milestones.md) for detailed milestones.

---

**Built with ❤️ for sales teams who need powerful call management and transcription capabilities.**
