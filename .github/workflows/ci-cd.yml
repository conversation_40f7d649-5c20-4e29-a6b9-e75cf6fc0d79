name: Sales Dialer CI/CD Pipeline

on:
  push:
    branches: [main, develop, 'feature/*']
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Lint and format check
  lint:
    name: Lint and Format Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies - Frontend
        run: |
          cd frontend
          npm ci

      - name: Install dependencies - API Gateway
        run: |
          cd backend/api-gateway
          npm ci

      - name: Install dependencies - Auth Service
        run: |
          cd backend/auth-service
          npm ci

      - name: Lint Frontend
        run: |
          cd frontend
          npm run lint

      - name: Lint API Gateway
        run: |
          cd backend/api-gateway
          npm run lint

      - name: Lint Auth Service
        run: |
          cd backend/auth-service
          npm run lint

      - name: Type Check Frontend
        run: |
          cd frontend
          npm run type-check

  # Unit tests
  test-unit:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: lint

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies - Frontend
        run: |
          cd frontend
          npm ci

      - name: Install dependencies - API Gateway
        run: |
          cd backend/api-gateway
          npm ci

      - name: Install dependencies - Auth Service
        run: |
          cd backend/auth-service
          npm ci

      - name: Run Frontend tests
        run: |
          cd frontend
          npm test -- --coverage --watchAll=false
        env:
          CI: true

      - name: Run API Gateway tests
        run: |
          cd backend/api-gateway
          npm test -- --coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-secret-key
          NODE_ENV: test

      - name: Run Auth Service tests
        run: |
          cd backend/auth-service
          npm test -- --coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-secret-key
          NODE_ENV: test

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: ./frontend/coverage/lcov.info,./backend/api-gateway/coverage/lcov.info,./backend/auth-service/coverage/lcov.info

  # Integration tests
  test-integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: test-unit

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd backend/api-gateway && npm ci
          cd ../auth-service && npm ci

      - name: Run database migrations
        run: |
          cd backend/api-gateway
          npm run migrate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Run integration tests
        run: |
          cd backend/api-gateway
          npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-secret-key
          NODE_ENV: test

  # Security scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: lint

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Run npm audit - Frontend
        run: |
          cd frontend
          npm audit --audit-level high

      - name: Run npm audit - API Gateway
        run: |
          cd backend/api-gateway
          npm audit --audit-level high

      - name: Run npm audit - Auth Service
        run: |
          cd backend/auth-service
          npm audit --audit-level high

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --all-projects --severity-threshold=high

  # Build Docker images
  build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test-unit, test-integration, security-scan]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            salesdialer/frontend
            salesdialer/api-gateway
            salesdialer/auth-service
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-

      - name: Build and push Frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile.dev
          push: true
          tags: salesdialer/frontend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push API Gateway image
        uses: docker/build-push-action@v5
        with:
          context: ./backend/api-gateway
          file: ./backend/api-gateway/Dockerfile.dev
          push: true
          tags: salesdialer/api-gateway:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Auth Service image
        uses: docker/build-push-action@v5
        with:
          context: ./backend/auth-service
          file: ./backend/auth-service/Dockerfile.dev
          push: true
          tags: salesdialer/auth-service:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment"
          echo "Image tags: ${{ github.sha }}"
          # Add actual deployment commands here

  # Deploy to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "Deploying to production environment"
          echo "Image tags: ${{ github.sha }}"
          # Add actual deployment commands here
