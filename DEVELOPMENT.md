# Sales Dialer Application - Development Guide

## Phase 1 Development Setup

This document provides a comprehensive guide for setting up and developing the Sales Dialer Application during Phase 1 of development.

## 🏗️ Project Structure

```
sales-dialer-ai-transcript/
├── .github/
│   └── workflows/
│       └── ci-cd.yml                 # CI/CD pipeline configuration
├── backend/
│   ├── api-gateway/                  # API Gateway service
│   │   ├── src/
│   │   │   ├── routes/              # API routes
│   │   │   ├── middleware/          # Express middleware
│   │   │   ├── services/            # Business logic
│   │   │   ├── utils/               # Utility functions
│   │   │   ├── __tests__/           # Unit tests
│   │   │   └── server.js            # Main server file
│   │   ├── Dockerfile.dev           # Development Docker configuration
│   │   ├── package.json             # Node.js dependencies
│   │   └── jest.config.js           # Jest test configuration
│   ├── auth-service/                # Authentication service
│   ├── contact-service/             # Contact management service
│   ├── telephony-service/           # Call management service
│   ├── transcription-service/       # AI transcription service
│   └── storage-service/             # File storage service
├── database/
│   ├── migrations/                  # Database migration files
│   │   └── 001_initial_schema.sql   # Initial database schema
│   ├── seeds/                       # Development seed data
│   │   └── 001_development_data.sql # Sample data for development
│   └── init/                        # Database initialization scripts
│       └── 01-init.sql              # Database setup script
├── frontend/
│   ├── public/                      # Static assets
│   ├── src/
│   │   ├── components/              # React components
│   │   ├── pages/                   # Page components
│   │   ├── hooks/                   # Custom React hooks
│   │   ├── services/                # API service functions
│   │   ├── store/                   # State management
│   │   ├── utils/                   # Utility functions
│   │   ├── types/                   # TypeScript type definitions
│   │   ├── __tests__/               # Frontend tests
│   │   ├── App.tsx                  # Main App component
│   │   └── index.tsx                # Application entry point
│   ├── Dockerfile.dev               # Development Docker configuration
│   └── package.json                 # Frontend dependencies
├── docs/                            # Project documentation
├── scripts/                         # Development and deployment scripts
│   ├── migrate.js                   # Database migration runner
│   └── setup-dev.sh                 # Development environment setup
├── docker-compose.dev.yml           # Development Docker Compose
├── .env.example                     # Environment variables template
├── .gitignore                       # Git ignore patterns
└── README.md                        # Project overview
```

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- Git

### 1. Clone and Setup

```bash
# Clone the repository
git clone https://github.com/c42705/sales-dialer-AI-transcript.git
cd sales-dialer-AI-transcript

# Switch to development branch
git checkout feature/phase1-development

# Run the setup script
./scripts/setup-dev.sh
```

### 2. Manual Setup (Alternative)

If the setup script doesn't work, follow these manual steps:

```bash
# Create environment file
cp .env.example .env

# Edit .env with your configuration
nano .env

# Start infrastructure services
docker-compose -f docker-compose.dev.yml up -d postgres redis minio n8n

# Wait for services to start
sleep 10

# Run database migrations
node scripts/migrate.js up

# Start all services
docker-compose -f docker-compose.dev.yml up -d
```

## 🛠️ Development Workflow

### Starting Development

```bash
# Start all services
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# View specific service logs
docker-compose -f docker-compose.dev.yml logs -f api-gateway
```

### Working with Services

Each service can be developed independently:

```bash
# Frontend development
cd frontend
npm start

# API Gateway development
cd backend/api-gateway
npm run dev

# Auth Service development
cd backend/auth-service
npm run dev
```

### Database Operations

```bash
# Run migrations
node scripts/migrate.js up

# Check migration status
node scripts/migrate.js status

# Rollback last migration
node scripts/migrate.js down

# Connect to database
docker-compose -f docker-compose.dev.yml exec postgres psql -U postgres -d salesdialer
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Frontend tests
cd frontend
npm test

# Backend tests
cd backend/api-gateway
npm test

# Run tests with coverage
npm run test:coverage
```

### Test Structure

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test service interactions
- **End-to-End Tests**: Test complete user workflows

## 📊 Monitoring and Debugging

### Service Health Checks

```bash
# Check API Gateway health
curl http://localhost:3001/api/v1/health

# Detailed health check
curl http://localhost:3001/api/v1/health/detailed
```

### Accessing Services

- **Frontend**: http://localhost:3000 (when started)
- **API Gateway**: http://localhost:3001
- **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin123)
- **n8n Workflows**: http://localhost:5678 (admin/admin123)
- **PostgreSQL**: localhost:5432 (postgres/password)
- **Redis**: localhost:6379

### Debugging

```bash
# View container logs
docker-compose -f docker-compose.dev.yml logs -f [service-name]

# Execute commands in containers
docker-compose -f docker-compose.dev.yml exec [service-name] bash

# Check container status
docker-compose -f docker-compose.dev.yml ps
```

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```bash
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/salesdialer

# JWT
JWT_SECRET=your-secret-key

# Twilio (for telephony)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token

# Google Drive (optional)
GOOGLE_DRIVE_FOLDER_ID=your_folder_id

# MinIO
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
```

### Service Configuration

Each service has its own configuration:

- **API Gateway**: Port 3001, handles routing and authentication
- **Auth Service**: Port 3002, manages user authentication
- **Contact Service**: Port 3003, manages contact data
- **Telephony Service**: Port 3004, handles call operations
- **Transcription Service**: Port 8000, processes audio transcription
- **Storage Service**: Port 3005, manages file storage

## 📝 Development Guidelines

### Code Style

- **Frontend**: TypeScript with ESLint and Prettier
- **Backend**: JavaScript with ESLint
- **Testing**: Jest for unit tests, React Testing Library for frontend

### Git Workflow

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add your feature description"

# Push to remote
git push origin feature/your-feature-name

# Create pull request
```

### Commit Convention

Follow conventional commits:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `test:` - Test additions or modifications
- `refactor:` - Code refactoring
- `chore:` - Maintenance tasks

## 🚨 Troubleshooting

### Common Issues

1. **Port conflicts**: Check if ports 3000-3005, 5432, 6379, 9000-9001, 5678 are available
2. **Docker issues**: Restart Docker daemon, check disk space
3. **Database connection**: Ensure PostgreSQL container is running and healthy
4. **Permission issues**: Check file permissions, especially for scripts

### Reset Development Environment

```bash
# Stop all services
docker-compose -f docker-compose.dev.yml down

# Remove volumes (WARNING: This will delete all data)
docker-compose -f docker-compose.dev.yml down -v

# Rebuild and restart
docker-compose -f docker-compose.dev.yml up -d --build
```

## 📚 Next Steps

After Phase 1 setup:

1. **Phase 2**: Implement telephony integration and call management
2. **Phase 3**: Add AI transcription and analytics
3. **Phase 4**: Complete integration and production readiness

See the [Development Phases document](docs/07-Development-Phases-Milestones.md) for detailed roadmap.

## 🤝 Contributing

1. Follow the development guidelines
2. Write tests for new features
3. Update documentation
4. Create pull requests for review

For detailed contribution guidelines, see the main README.md file.
